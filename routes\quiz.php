<?php

use Illuminate\Support\Facades\Route;
use App\Http\Controllers\PublicQuizController;

/*==============================================================
======================< Public Quiz Routes >=================
==============================================================*/

Route::controller(PublicQuizController::class)->prefix('quiz')->name('quiz.')->group(function () {
    // Walk-in candidate registration
    Route::get('/test', 'index')->name('register');
    Route::post('/test', 'createWalkInTest')->name('create-walk-in');
    
    // Quiz taking routes
    Route::get('/test/{testId}', 'showTest')->name('test.show');
    Route::post('/test/{testId}/start', 'startTest')->name('test.start');
    Route::post('/test/{testId}/answer', 'submitAnswer')->name('test.answer');
    Route::post('/test/{testId}/submit', 'submitQuiz')->name('test.submit');
    Route::post('/test/{testId}/auto-submit', 'autoSubmitQuiz')->name('test.auto-submit');
    
    // Results and status
    Route::get('/test/{testId}/results', 'showResults')->name('test.results');
    Route::get('/test/{testId}/status', 'checkTestStatus')->name('test.status');
});
