a:4:{s:6:"_token";s:40:"eOkc2I8kqyfBo7tznvkk5NThk1J8mXEO7al74BxR";s:9:"_previous";a:1:{s:3:"url";s:62:"https://blueorange.test/application/quiz/test/dwODpVQANx1aBgnP";}s:6:"_flash";a:2:{s:3:"old";a:24:{i:0;s:18:"alert.config.title";i:1;s:17:"alert.config.text";i:2;s:18:"alert.config.timer";i:3;s:18:"alert.config.width";i:4;s:23:"alert.config.heightAuto";i:5;s:20:"alert.config.padding";i:6;s:30:"alert.config.showConfirmButton";i:7;s:28:"alert.config.showCloseButton";i:8;s:29:"alert.config.timerProgressBar";i:9;s:24:"alert.config.customClass";i:10;s:17:"alert.config.icon";i:11;s:12:"alert.config";i:12;s:18:"alert.config.title";i:13;s:17:"alert.config.text";i:14;s:18:"alert.config.timer";i:15;s:18:"alert.config.width";i:16;s:23:"alert.config.heightAuto";i:17;s:20:"alert.config.padding";i:18;s:30:"alert.config.showConfirmButton";i:19;s:28:"alert.config.showCloseButton";i:20;s:29:"alert.config.timerProgressBar";i:21;s:24:"alert.config.customClass";i:22;s:17:"alert.config.icon";i:23;s:12:"alert.config";}s:3:"new";a:0:{}}s:5:"alert";a:1:{s:6:"config";s:472:"{"title":"Quiz test created successfully! You have 10 minutes to complete 10 questions.","text":"","timer":"5000","width":"32rem","heightAuto":true,"padding":"1.25rem","showConfirmButton":true,"showCloseButton":true,"timerProgressBar":true,"customClass":{"container":null,"popup":null,"header":null,"title":null,"closeButton":null,"icon":null,"image":null,"content":null,"input":null,"actions":null,"confirmButton":null,"cancelButton":null,"footer":null},"icon":"success"}";}}