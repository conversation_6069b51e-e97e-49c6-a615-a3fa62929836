<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('All Tests')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <!-- DataTables css -->
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/dataTables.bootstrap4.min.css')); ?>" rel="stylesheet" type="text/css" />
    <link href="<?php echo e(asset('assets/css/custom_css/datatables/datatable.css')); ?>" rel="stylesheet" type="text/css" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        /* Custom CSS Here */
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('All Tests')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.dashboard.index')); ?>"><?php echo e(__('Dashboard')); ?></a>
    </li>
    <li class="breadcrumb-item"><?php echo e(__('Quiz')); ?></li>
    <li class="breadcrumb-item"><?php echo e(__('Quiz Tests')); ?></li>
    <li class="breadcrumb-item active"><?php echo e(__('All Tests')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Basic Bootstrap Table -->
<div class="card">
    <div class="card-header d-flex justify-content-between align-items-center">
        <h5 class="mb-0"><?php echo e(__('All Tests')); ?></h5>
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Create'])): ?>
            <a href="<?php echo e(route('administration.quiz.test.create')); ?>" class="btn btn-primary btn-sm">
                <i class="ti ti-plus me-1"></i><?php echo e(__('Create Test')); ?>

            </a>
        <?php endif; ?>
    </div>

    <div class="card-body">
        <div class="table-responsive-md table-responsive-sm w-100">
            <table class="table data-table table-bordered">
                <thead>
                    <tr>
                        <th><?php echo e(__('SL')); ?></th>
                        <th><?php echo e(__('Candidate Name & Email')); ?></th>
                        <th><?php echo e(__('Creator')); ?></th>
                        <th><?php echo e(__('Created At')); ?></th>
                        <th class="text-center"><?php echo e(__('Total Questions')); ?></th>
                        <th class="text-center"><?php echo e(__('Result')); ?></th>
                        <th class="text-center"><?php echo e(__('Actions')); ?></th>
                    </tr>
                </thead>
                <tbody>
                    <?php $__empty_1 = true; $__currentLoopData = $tests; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $test): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); $__empty_1 = false; ?>
                        <tr>
                            <td><?php echo e($loop->iteration); ?></td>
                            <td>
                                <?php echo e($test->candidate_name); ?><br>
                                <small class="text-muted"><?php echo e($test->candidate_email); ?></small>
                            </td>
                            <td>
                                <?php echo show_user_name_and_avatar($test->creator, name: null); ?>

                            </td>
                            <td>
                                <?php echo e(date_time_ago($test->created_at)); ?>

                            </td>
                            <td class="text-center">
                                <span class="badge bg-label-dark text-bold"><?php echo e($test->questions()->count()); ?></span>
                            </td>
                            <td class="text-center">
                                <?php if(!is_null($test->total_score)): ?>
                                    <span class="badge bg-label-dark text-bold"><?php echo e($test->total_score); ?></span>
                                <?php endif; ?>
                            </td>
                            <td class="text-center">
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Delete'])): ?>
                                    <a href="<?php echo e(route('administration.quiz.test.destroy', ['test' => $test])); ?>" class="btn btn-sm btn-icon btn-danger confirm-danger" data-bs-toggle="tooltip" title="Delete Test?">
                                        <i class="ti ti-trash"></i>
                                    </a>
                                <?php endif; ?>
                                <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->any(['Quiz Everything', 'Quiz Read'])): ?>
                                    <a href="<?php echo e(route('administration.quiz.test.show', $test)); ?>" class="btn btn-sm btn-icon btn-primary" data-bs-toggle="tooltip" title="Show Details">
                                        <i class="ti ti-info-hexagon"></i>
                                    </a>
                                <?php endif; ?>
                            </td>
                        </tr>
                    <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); if ($__empty_1): ?>
                        <tr>
                            <td colspan="6" class="text-center"><?php echo e(__('No tests found')); ?></td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
    </div>
</div>
<!--/ Basic Bootstrap Table -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <!-- Datatable js -->
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/jquery.dataTables.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/dataTables.bootstrap4.min.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/js/custom_js/datatables/datatable.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            //
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/quiz/test/index.blade.php ENDPATH**/ ?>