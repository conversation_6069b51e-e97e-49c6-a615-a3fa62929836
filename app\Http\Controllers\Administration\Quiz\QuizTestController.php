<?php

namespace App\Http\Controllers\Administration\Quiz;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Http\Controllers\Controller;
use App\Models\Quiz\QuizTest\QuizTest;
use App\Models\Quiz\QuizQuestion\QuizQuestion;

class QuizTestController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $tests = QuizTest::with([
            'creator.employee',
            'creator.media',
            'creator.roles'
        ])->orderByDesc('created_at')->get();

        return view('administration.quiz.test.index', compact(['tests']));
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $questions = QuizQuestion::where('is_active', true)->get();
        return view('administration.quiz.test.create', compact(['questions']));
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        $request->validate([
            'candidate_name' => 'required|string|max:255',
            'candidate_email' => 'required|email|max:255',
            'total_questions' => 'required|integer|min:1|max:100',
            'total_time' => 'required|integer|min:1|max:300',
            'passing_score' => 'required|integer|min:1',
            'question_ids' => 'nullable|array',
            'question_ids.*' => 'exists:quiz_questions,id',
        ]);

        // Validate passing score is not greater than total questions
        if ($request->passing_score > $request->total_questions) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['passing_score' => 'Passing score cannot be greater than total questions.']);
        }

        try {
            DB::transaction(function () use ($request) {
                $questionIds = $request->question_ids;

                // If no questions selected, randomly select from active questions
                if (empty($questionIds)) {
                    $availableQuestions = QuizQuestion::where('is_active', true)->pluck('id')->toArray();

                    if (count($availableQuestions) < $request->total_questions) {
                        throw new Exception('Not enough active questions available. Please add more questions or reduce the total questions count.');
                    }

                    $questionIds = array_slice(
                        array_rand(array_flip($availableQuestions), $request->total_questions),
                        0,
                        $request->total_questions
                    );
                } else {
                    // If questions are selected, ensure we have the right amount
                    if (count($questionIds) > $request->total_questions) {
                        $questionIds = array_slice($questionIds, 0, $request->total_questions);
                    } elseif (count($questionIds) < $request->total_questions) {
                        // Fill remaining with random questions
                        $remainingCount = $request->total_questions - count($questionIds);
                        $availableQuestions = QuizQuestion::where('is_active', true)
                            ->whereNotIn('id', $questionIds)
                            ->pluck('id')
                            ->toArray();

                        if (count($availableQuestions) < $remainingCount) {
                            throw new Exception('Not enough additional questions available to reach the total questions count.');
                        }

                        $additionalQuestions = array_slice(
                            array_rand(array_flip($availableQuestions), $remainingCount),
                            0,
                            $remainingCount
                        );

                        $questionIds = array_merge($questionIds, $additionalQuestions);
                    }
                }

                QuizTest::create([
                    'creator_id' => auth()->id(),
                    'candidate_name' => $request->candidate_name,
                    'candidate_email' => $request->candidate_email,
                    'total_questions' => $request->total_questions,
                    'total_time' => $request->total_time,
                    'passing_score' => $request->passing_score,
                    'question_ids' => $questionIds,
                    'status' => 'Pending',
                ]);
            });

            toast('Quiz test created successfully.', 'success');
            return redirect()->route('administration.quiz.test.index');
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Display the specified resource.
     */
    public function show(QuizTest $test)
    {
        $test->load([
            'creator.employee',
            'creator.media',
            'creator.roles',
            'answers.question'
        ]);

        $questions = $test->questions();

        return view('administration.quiz.test.show', compact(['test', 'questions']));
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(QuizTest $test)
    {
        if ($test->status !== 'Pending') {
            alert('Error', 'Cannot edit test that has been started or completed.', 'error');
            return redirect()->back();
        }

        $questions = QuizQuestion::where('is_active', true)->get();
        $selectedQuestions = $test->question_ids ?? [];

        return view('administration.quiz.test.edit', compact(['test', 'questions', 'selectedQuestions']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, QuizTest $test)
    {
        if ($test->status !== 'Pending') {
            alert('Error', 'Cannot update test that has been started or completed.', 'error');
            return redirect()->back();
        }

        $request->validate([
            'candidate_name' => 'required|string|max:255',
            'candidate_email' => 'required|email|max:255',
            'total_questions' => 'required|integer|min:1|max:100',
            'total_time' => 'required|integer|min:1|max:300',
            'passing_score' => 'required|integer|min:1',
            'question_ids' => 'nullable|array',
            'question_ids.*' => 'exists:quiz_questions,id',
        ]);

        // Validate passing score is not greater than total questions
        if ($request->passing_score > $request->total_questions) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['passing_score' => 'Passing score cannot be greater than total questions.']);
        }

        try {
            DB::transaction(function () use ($request, $test) {
                $questionIds = $request->question_ids;

                // If no questions selected, randomly select from active questions
                if (empty($questionIds)) {
                    $availableQuestions = QuizQuestion::where('is_active', true)->pluck('id')->toArray();

                    if (count($availableQuestions) < $request->total_questions) {
                        throw new Exception('Not enough active questions available. Please add more questions or reduce the total questions count.');
                    }

                    $questionIds = array_slice(
                        array_rand(array_flip($availableQuestions), $request->total_questions),
                        0,
                        $request->total_questions
                    );
                } else {
                    // If questions are selected, ensure we have the right amount
                    if (count($questionIds) > $request->total_questions) {
                        $questionIds = array_slice($questionIds, 0, $request->total_questions);
                    } elseif (count($questionIds) < $request->total_questions) {
                        // Fill remaining with random questions
                        $remainingCount = $request->total_questions - count($questionIds);
                        $availableQuestions = QuizQuestion::where('is_active', true)
                            ->whereNotIn('id', $questionIds)
                            ->pluck('id')
                            ->toArray();

                        if (count($availableQuestions) < $remainingCount) {
                            throw new Exception('Not enough additional questions available to reach the total questions count.');
                        }

                        $additionalQuestions = array_slice(
                            array_rand(array_flip($availableQuestions), $remainingCount),
                            0,
                            $remainingCount
                        );

                        $questionIds = array_merge($questionIds, $additionalQuestions);
                    }
                }

                $test->update([
                    'candidate_name' => $request->candidate_name,
                    'candidate_email' => $request->candidate_email,
                    'total_questions' => $request->total_questions,
                    'total_time' => $request->total_time,
                    'passing_score' => $request->passing_score,
                    'question_ids' => $questionIds,
                ]);
            });

            toast('Quiz test updated successfully.', 'success');
            return redirect()->route('administration.quiz.test.show', $test);
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back()->withInput();
        }
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(QuizTest $test)
    {
        try {
            if ($test->status === 'Running') {
                alert('Error', 'Cannot delete a test that is currently running.', 'error');
                return redirect()->back();
            }

            $test->delete();

            toast('Quiz test deleted successfully.', 'success');
            return redirect()->route('administration.quiz.test.index');
        } catch (Exception $e) {
            alert('Oops! Error.', $e->getMessage(), 'error');
            return redirect()->back();
        }
    }
}
