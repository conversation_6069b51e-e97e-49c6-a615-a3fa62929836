@extends('layouts.public.quiz')

@section('page_title', __('QUIZ TEST'))

@section('custom_css')
    <style>
        .quiz-header {
            background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin-bottom: 25px;
            box-shadow: 0 4px 12px rgba(105, 108, 255, 0.3);
        }

        .timer-display {
            font-size: 1.8rem;
            font-weight: bold;
            color: #fff;
            text-shadow: 0 2px 4px rgba(0,0,0,0.3);
        }

        .timer-warning {
            color: #ff6b6b !important;
            animation: pulse 1s infinite;
        }

        @keyframes pulse {
            0% { opacity: 1; }
            50% { opacity: 0.7; }
            100% { opacity: 1; }
        }

        .question-card {
            border: 1px solid #e3e6f0;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .question-card:hover {
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .question-number {
            background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
            color: white;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-bottom: 20px;
            font-size: 1.1rem;
            box-shadow: 0 2px 8px rgba(105, 108, 255, 0.3);
        }

        .option-item {
            border: 2px solid #e3e6f0;
            border-radius: 12px;
            padding: 18px;
            margin-bottom: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            background: white;
        }

        .option-item:hover {
            border-color: #696cff;
            background-color: #f8f9ff;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(105, 108, 255, 0.15);
        }

        .option-item.selected {
            border-color: #696cff;
            background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
            color: white;
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(105, 108, 255, 0.3);
        }

        .option-letter {
            background: rgba(105, 108, 255, 0.1);
            color: #696cff;
            border-radius: 50%;
            width: 35px;
            height: 35px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            margin-right: 15px;
            transition: all 0.3s ease;
        }

        .option-item.selected .option-letter {
            background: rgba(255,255,255,0.2);
            color: white;
        }

        .quiz-navigation {
            position: fixed;
            bottom: 25px;
            right: 25px;
            background: white;
            padding: 20px;
            border-radius: 15px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.15);
            z-index: 1000;
        }

        .progress-indicator {
            margin-bottom: 25px;
        }

        .progress-bar {
            background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
            transition: width 0.3s ease;
        }

        .start-quiz-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            border: 2px solid #696cff;
            border-radius: 20px;
            padding: 40px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(105, 108, 255, 0.15);
        }

        .quiz-icon {
            font-size: 4rem;
            color: #696cff;
            margin-bottom: 20px;
            text-shadow: 0 4px 8px rgba(105, 108, 255, 0.3);
        }
    </style>
@endsection

@section('content')
    <!-- Quiz Header -->
    <div class="quiz-header">
        <div class="row align-items-center">
            <div class="col-md-6">
                <h4 class="mb-1"><i class="ti ti-user me-2"></i>{{ $test->candidate_name }}</h4>
                <p class="mb-0"><i class="ti ti-mail me-2"></i>{{ $test->candidate_email }}</p>
            </div>
            <div class="col-md-6 text-md-end">
                <div class="timer-display" id="timer">
                    @if($test->status === 'Pending')
                        <i class="ti ti-clock me-2"></i>Ready to Start
                    @else
                        <i class="ti ti-clock me-2"></i>--:--
                    @endif
                </div>
                <small><i class="ti ti-hourglass-high me-1"></i>Time Remaining</small>
            </div>
        </div>
    </div>

    <!-- Progress Indicator -->
    <div class="progress-indicator">
        <div class="card">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center mb-3">
                    <h6 class="mb-0"><i class="ti ti-chart-line me-2"></i>Quiz Progress</h6>
                    <span id="progress-text" class="badge bg-primary">0 of {{ $test->total_questions }} answered</span>
                </div>
                <div class="progress" style="height: 8px;">
                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>
        </div>
    </div>

    @if($test->status === 'Pending')
        <!-- Start Quiz Section -->
        <div class="start-quiz-card" id="start-section">
            <i class="ti ti-brain quiz-icon"></i>
            <h4 class="mb-3">Ready to Start Your Quiz?</h4>
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="ti ti-list-numbers text-primary mb-2" style="font-size: 2rem;"></i>
                        <h6>Questions</h6>
                        <strong>{{ $test->total_questions }}</strong>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="ti ti-clock text-warning mb-2" style="font-size: 2rem;"></i>
                        <h6>Time Limit</h6>
                        <strong>{{ $test->total_time }} min</strong>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="ti ti-target text-success mb-2" style="font-size: 2rem;"></i>
                        <h6>Passing Score</h6>
                        <strong>{{ $test->passing_score }}/{{ $test->total_questions }}</strong>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="text-center">
                        <i class="ti ti-id text-info mb-2" style="font-size: 2rem;"></i>
                        <h6>Test ID</h6>
                        <strong>{{ $test->getRouteKey() }}</strong>
                    </div>
                </div>
            </div>
            <button class="btn btn-primary btn-lg px-5" id="start-quiz-btn">
                <i class="ti ti-play me-2"></i>Start Quiz Now
            </button>
        </div>
    @endif

    <!-- Quiz Questions Section -->
    <div id="quiz-section" style="{{ $test->status === 'Pending' ? 'display: none;' : '' }}">
        @foreach($questions as $index => $question)
            <div class="question-card" data-question-id="{{ $question->id }}" style="{{ $index > 0 ? 'display: none;' : '' }}">
                <div class="d-flex align-items-center mb-3">
                    <div class="question-number">{{ $index + 1 }}</div>
                    <div class="ms-3">
                        <span class="badge bg-light text-dark">Question {{ $index + 1 }} of {{ $test->total_questions }}</span>
                    </div>
                </div>

                <h5 class="question-text mb-4">{{ $question->question }}</h5>

                <div class="options">
                    @foreach(['A', 'B', 'C', 'D'] as $option)
                        <div class="option-item" data-option="{{ $option }}" data-question-id="{{ $question->id }}">
                            <div class="d-flex align-items-center">
                                <div class="option-letter">
                                    {{ $option }}
                                </div>
                                <div class="option-text flex-grow-1">
                                    {{ $question->{'option_' . strtolower($option)} }}
                                </div>
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>
        @endforeach
    </div>

    <!-- Quiz Navigation -->
    <div class="quiz-navigation" id="quiz-nav" style="{{ $test->status === 'Pending' ? 'display: none;' : '' }}">
        <div class="d-flex flex-column gap-2">
            <div class="d-flex gap-2">
                <button class="btn btn-outline-secondary" id="prev-btn" disabled>
                    <i class="ti ti-chevron-left"></i> Previous
                </button>
                <button class="btn btn-outline-primary" id="next-btn">
                    Next <i class="ti ti-chevron-right"></i>
                </button>
            </div>
            <button class="btn btn-success w-100" id="submit-btn" style="display: none;">
                <i class="ti ti-check"></i> Submit Quiz
            </button>
        </div>
    </div>
@endsection

@section('custom_script')

    <script>
        $(document).ready(function() {
            let currentQuestion = 0;
            let totalQuestions = {{ $test->total_questions }};
            let answeredQuestions = 0;
            let testStarted = {{ $test->status === 'Running' ? 'true' : 'false' }};
            let timeRemaining = {{ $test->total_time * 60 }}; // in seconds
            let timerInterval;
            let existingAnswers = @json($existingAnswers);

            // Load existing answers
            Object.keys(existingAnswers).forEach(questionId => {
                const option = existingAnswers[questionId];
                $(`.option-item[data-question-id="${questionId}"][data-option="${option}"]`).addClass('selected');
            });

            // Update answered count
            updateProgress();

            // Start quiz
            $('#start-quiz-btn').on('click', function() {
                $(this).prop('disabled', true).html('<i class="ti ti-loader me-2"></i>Starting...');

                $.post('{{ route('application.quiz.test.start', $test->getRouteKey()) }}', {
                    _token: '{{ csrf_token() }}'
                }).done(function(response) {
                    if (response.success) {
                        testStarted = true;
                        $('#start-section').fadeOut(300, function() {
                            $('#quiz-section, #quiz-nav').fadeIn(300);
                        });
                        startTimer();
                        toast('Quiz started successfully! Good luck!', 'success');
                    }
                }).fail(function(xhr) {
                    $('#start-quiz-btn').prop('disabled', false).html('<i class="ti ti-play me-2"></i>Start Quiz Now');
                    alert('Error starting quiz: ' + (xhr.responseJSON?.error || 'Unknown error'));
                });
            });

            // Option selection
            $('.option-item').on('click', function() {
                const questionId = $(this).data('question-id');
                const option = $(this).data('option');

                // Remove selection from other options for this question
                $(`.option-item[data-question-id="${questionId}"]`).removeClass('selected');

                // Select this option
                $(this).addClass('selected');

                // Submit answer
                submitAnswer(questionId, option);
            });

            // Navigation
            $('#next-btn').on('click', function() {
                if (currentQuestion < totalQuestions - 1) {
                    currentQuestion++;
                    showQuestion(currentQuestion);
                }
            });

            $('#prev-btn').on('click', function() {
                    if (currentQuestion > 0) {
                        currentQuestion--;
                        showQuestion(currentQuestion);
                    }
                });

                // Submit quiz
                $('#submit-btn').on('click', function() {
                    if (confirm('Are you sure you want to submit your quiz? You cannot change your answers after submission.')) {
                        submitQuiz();
                    }
                });

                function showQuestion(index) {
                    $('.question-card').hide();
                    $('.question-card').eq(index).show();

                    // Update navigation buttons
                    $('#prev-btn').prop('disabled', index === 0);
                    $('#next-btn').toggle(index < totalQuestions - 1);
                    $('#submit-btn').toggle(index === totalQuestions - 1);
                }

            function submitAnswer(questionId, option) {
                $.post('{{ route('application.quiz.test.answer', $test->getRouteKey()) }}', {
                    _token: '{{ csrf_token() }}',
                    question_id: questionId,
                    selected_option: option
                }).done(function(response) {
                    if (response.success) {
                        updateProgress();
                    }
                }).fail(function(xhr) {
                    console.error('Error submitting answer:', xhr.responseJSON?.error);
                });
            }

            function updateProgress() {
                answeredQuestions = $('.option-item.selected').length;
                const percentage = (answeredQuestions / totalQuestions) * 100;
                $('#progress-bar').css('width', percentage + '%');
                $('#progress-text').text(answeredQuestions + ' of ' + totalQuestions + ' answered').removeClass('bg-primary bg-success bg-warning').addClass(
                    percentage === 100 ? 'bg-success' : percentage > 50 ? 'bg-primary' : 'bg-warning'
                );
            }

            function startTimer() {
                timerInterval = setInterval(function() {
                    timeRemaining--;

                    const minutes = Math.floor(timeRemaining / 60);
                    const seconds = timeRemaining % 60;

                    const timeDisplay = String(minutes).padStart(2, '0') + ':' + String(seconds).padStart(2, '0');
                    $('#timer').html('<i class="ti ti-clock me-2"></i>' + timeDisplay);

                    // Add warning class when time is low
                    if (timeRemaining <= 300) { // 5 minutes
                        $('#timer').addClass('timer-warning');
                    }

                    if (timeRemaining <= 0) {
                        clearInterval(timerInterval);
                        autoSubmitQuiz();
                    }
                }, 1000);
            }

            function submitQuiz() {
                $('#submit-btn').prop('disabled', true).html('<i class="ti ti-loader me-2"></i>Submitting...');

                $.post('{{ route('application.quiz.test.submit', $test->getRouteKey()) }}', {
                    _token: '{{ csrf_token() }}'
                }).done(function(response) {
                    if (response.success) {
                        toast('Quiz submitted successfully!', 'success');
                        setTimeout(() => {
                            window.location.href = response.redirect_url;
                        }, 1000);
                    }
                }).fail(function(xhr) {
                    $('#submit-btn').prop('disabled', false).html('<i class="ti ti-check"></i> Submit Quiz');
                    alert('Error submitting quiz: ' + (xhr.responseJSON?.error || 'Unknown error'));
                });
            }

            function autoSubmitQuiz() {
                $.post('{{ route('application.quiz.test.auto-submit', $test->getRouteKey()) }}', {
                    _token: '{{ csrf_token() }}'
                }).done(function(response) {
                    if (response.success) {
                        alert('Time is up! Your quiz has been automatically submitted.');
                        window.location.href = response.redirect_url;
                    }
                });
            }

            // Start timer if quiz is already running
            if (testStarted) {
                // Get remaining time from server
                $.get('{{ route('application.quiz.test.status', $test->getRouteKey()) }}').done(function(response) {
                    if (response.time_remaining > 0) {
                        timeRemaining = response.time_remaining;
                        startTimer();
                    } else {
                        autoSubmitQuiz();
                    }
                });
            }
        });
    </script>
@endsection
