<!DOCTYPE html>

<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light-style layout-wide customizer-hide" dir="ltr" data-theme="theme-default" data-assets-path="../../assets/" data-template="">
    <head>
        <meta charset="utf-8" />
        <!-- CSRF Token -->
        <meta name="csrf-token" content="{{ csrf_token() }}" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

        <title>{{ config('app.name') }} || {{ __('QUIZ TEST') }}</title>

        <meta name="description" content="Blue Orange Web Application | Staff-India" />

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset(config('app.favicon')) }}" />

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&ampdisplay=swap" rel="stylesheet" />

        <!-- Icons -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/fontawesome.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/tabler-icons.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/flag-icons.css') }}" />

        <!-- Core CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/rtl/core.css') }}" class="template-customizer-core-css" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/rtl/theme-default.css') }}" class="template-customizer-theme-css" />
        <link rel="stylesheet" href="{{ asset('assets/css/demo.css') }}" />

        <!-- Vendors CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/node-waves/node-waves.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/typeahead-js/typeahead.css') }}" />

        <!-- Page CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/page-auth.css') }}" />

        <!-- Helpers -->
        <script src="{{ asset('assets/vendor/js/helpers.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/template-customizer.js') }}"></script>
        <script src="{{ asset('assets/js/config.js') }}"></script>

        <style>
            .quiz-container {
                max-width: 800px;
                margin: 0 auto;
                padding: 20px;
            }

            .quiz-header {
                background: linear-gradient(135deg, #696cff 0%, #5a67d8 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                margin-bottom: 20px;
            }

            .timer-display {
                font-size: 1.5rem;
                font-weight: bold;
                color: #ff6b6b;
            }

            .question-card {
                border: 1px solid #e3e6f0;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                background: white;
                box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            }

            .question-number {
                background: #696cff;
                color: white;
                border-radius: 50%;
                width: 30px;
                height: 30px;
                display: flex;
                align-items: center;
                justify-content: center;
                font-weight: bold;
                margin-bottom: 15px;
            }

            .option-item {
                border: 2px solid #e3e6f0;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 10px;
                cursor: pointer;
                transition: all 0.3s ease;
            }

            .option-item:hover {
                border-color: #696cff;
                background-color: #f8f9ff;
            }

            .option-item.selected {
                border-color: #696cff;
                background-color: #696cff;
                color: white;
            }

            .quiz-navigation {
                position: fixed;
                bottom: 20px;
                right: 20px;
                background: white;
                padding: 15px;
                border-radius: 10px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            }

            .progress-indicator {
                margin-bottom: 20px;
            }
        </style>
    </head>

    <body>
        <!-- Content -->
        <div class="quiz-container">
            <!-- Quiz Header -->
            <div class="quiz-header">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <h4 class="mb-1">{{ $test->candidate_name }}</h4>
                        <p class="mb-0">{{ $test->candidate_email }}</p>
                    </div>
                    <div class="col-md-6 text-md-end">
                        <div class="timer-display" id="timer">
                            @if($test->status === 'Pending')
                                Ready to Start
                            @else
                                --:--
                            @endif
                        </div>
                        <small>Time Remaining</small>
                    </div>
                </div>
            </div>

            <!-- Progress Indicator -->
            <div class="progress-indicator">
                <div class="d-flex justify-content-between align-items-center mb-2">
                    <span>Progress</span>
                    <span id="progress-text">0 of {{ $test->total_questions }} answered</span>
                </div>
                <div class="progress">
                    <div class="progress-bar" role="progressbar" style="width: 0%" id="progress-bar"></div>
                </div>
            </div>

            @if($test->status === 'Pending')
                <!-- Start Quiz Section -->
                <div class="card text-center" id="start-section">
                    <div class="card-body">
                        <i class="ti ti-brain mb-3" style="font-size: 3rem; color: #696cff;"></i>
                        <h5 class="card-title">Ready to Start Your Quiz?</h5>
                        <p class="card-text">
                            You have <strong>{{ $test->total_time }} minutes</strong> to answer <strong>{{ $test->total_questions }} questions</strong>.<br>
                            You need to score at least <strong>{{ $test->passing_score }} out of {{ $test->total_questions }}</strong> to pass.
                        </p>
                        <button class="btn btn-primary btn-lg" id="start-quiz-btn">
                            <i class="ti ti-play me-2"></i>Start Quiz
                        </button>
                    </div>
                </div>
            @endif

            <!-- Quiz Questions Section -->
            <div id="quiz-section" style="{{ $test->status === 'Pending' ? 'display: none;' : '' }}">
                @foreach($questions as $index => $question)
                    <div class="question-card" data-question-id="{{ $question->id }}" style="{{ $index > 0 ? 'display: none;' : '' }}">
                        <div class="question-number">{{ $index + 1 }}</div>
                        <h6 class="question-text">{{ $question->question }}</h6>

                        <div class="options mt-3">
                            @foreach(['A', 'B', 'C', 'D'] as $option)
                                <div class="option-item" data-option="{{ $option }}" data-question-id="{{ $question->id }}">
                                    <div class="d-flex align-items-center">
                                        <div class="option-letter me-3">
                                            <strong>{{ $option }}.</strong>
                                        </div>
                                        <div class="option-text">
                                            {{ $question->{'option_' . strtolower($option)} }}
                                        </div>
                                    </div>
                                </div>
                            @endforeach
                        </div>
                    </div>
                @endforeach
            </div>

            <!-- Quiz Navigation -->
            <div class="quiz-navigation" id="quiz-nav" style="{{ $test->status === 'Pending' ? 'display: none;' : '' }}">
                <div class="d-flex gap-2">
                    <button class="btn btn-outline-secondary" id="prev-btn" disabled>
                        <i class="ti ti-chevron-left"></i> Previous
                    </button>
                    <button class="btn btn-outline-primary" id="next-btn">
                        Next <i class="ti ti-chevron-right"></i>
                    </button>
                    <button class="btn btn-success" id="submit-btn" style="display: none;">
                        <i class="ti ti-check"></i> Submit Quiz
                    </button>
                </div>
            </div>
        </div>

        <!-- Core JS -->
        <script src="{{ asset('assets/vendor/libs/jquery/jquery.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/popper/popper.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/bootstrap.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/node-waves/node-waves.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/hammer/hammer.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/i18n/i18n.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/typeahead-js/typeahead.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/menu.js') }}"></script>
        <script src="{{ asset('assets/js/main.js') }}"></script>

        @include('sweetalert::alert')

        <script>
            $(document).ready(function() {
                let currentQuestion = 0;
                let totalQuestions = {{ $test->total_questions }};
                let answeredQuestions = 0;
                let testStarted = {{ $test->status === 'Running' ? 'true' : 'false' }};
                let timeRemaining = {{ $test->total_time * 60 }}; // in seconds
                let timerInterval;
                let existingAnswers = @json($existingAnswers);

                // Load existing answers
                Object.keys(existingAnswers).forEach(questionId => {
                    const option = existingAnswers[questionId];
                    $(`.option-item[data-question-id="${questionId}"][data-option="${option}"]`).addClass('selected');
                });

                // Update answered count
                updateProgress();

                // Start quiz
                $('#start-quiz-btn').on('click', function() {
                    $.post('{{ route('application.quiz.test.start', $test->getRouteKey()) }}', {
                        _token: '{{ csrf_token() }}'
                    }).done(function(response) {
                        if (response.success) {
                            testStarted = true;
                            $('#start-section').hide();
                            $('#quiz-section, #quiz-nav').show();
                            startTimer();
                        }
                    }).fail(function(xhr) {
                        alert('Error starting quiz: ' + xhr.responseJSON.error);
                    });
                });

                // Option selection
                $('.option-item').on('click', function() {
                    const questionId = $(this).data('question-id');
                    const option = $(this).data('option');

                    // Remove selection from other options for this question
                    $(`.option-item[data-question-id="${questionId}"]`).removeClass('selected');

                    // Select this option
                    $(this).addClass('selected');

                    // Submit answer
                    submitAnswer(questionId, option);
                });

                // Navigation
                $('#next-btn').on('click', function() {
                    if (currentQuestion < totalQuestions - 1) {
                        currentQuestion++;
                        showQuestion(currentQuestion);
                    }
                });

                $('#prev-btn').on('click', function() {
                    if (currentQuestion > 0) {
                        currentQuestion--;
                        showQuestion(currentQuestion);
                    }
                });

                // Submit quiz
                $('#submit-btn').on('click', function() {
                    if (confirm('Are you sure you want to submit your quiz? You cannot change your answers after submission.')) {
                        submitQuiz();
                    }
                });

                function showQuestion(index) {
                    $('.question-card').hide();
                    $('.question-card').eq(index).show();

                    // Update navigation buttons
                    $('#prev-btn').prop('disabled', index === 0);
                    $('#next-btn').toggle(index < totalQuestions - 1);
                    $('#submit-btn').toggle(index === totalQuestions - 1);
                }

                function submitAnswer(questionId, option) {
                    $.post('{{ route('application.quiz.test.answer', $test->getRouteKey()) }}', {
                        _token: '{{ csrf_token() }}',
                        question_id: questionId,
                        selected_option: option
                    }).done(function(response) {
                        if (response.success) {
                            updateProgress();
                        }
                    });
                }

                function updateProgress() {
                    answeredQuestions = $('.option-item.selected').length;
                    const percentage = (answeredQuestions / totalQuestions) * 100;
                    $('#progress-bar').css('width', percentage + '%');
                    $('#progress-text').text(answeredQuestions + ' of ' + totalQuestions + ' answered');
                }

                function startTimer() {
                    timerInterval = setInterval(function() {
                        timeRemaining--;

                        const minutes = Math.floor(timeRemaining / 60);
                        const seconds = timeRemaining % 60;

                        $('#timer').text(
                            String(minutes).padStart(2, '0') + ':' +
                            String(seconds).padStart(2, '0')
                        );

                        if (timeRemaining <= 0) {
                            clearInterval(timerInterval);
                            autoSubmitQuiz();
                        }
                    }, 1000);
                }

                function submitQuiz() {
                    $.post('{{ route('application.quiz.test.submit', $test->getRouteKey()) }}', {
                        _token: '{{ csrf_token() }}'
                    }).done(function(response) {
                        if (response.success) {
                            window.location.href = response.redirect_url;
                        }
                    });
                }

                function autoSubmitQuiz() {
                    $.post('{{ route('quiz.test.auto-submit', $test->getRouteKey()) }}', {
                        _token: '{{ csrf_token() }}'
                    }).done(function(response) {
                        if (response.success) {
                            alert('Time is up! Your quiz has been automatically submitted.');
                            window.location.href = response.redirect_url;
                        }
                    });
                }

                // Start timer if quiz is already running
                if (testStarted) {
                    // Get remaining time from server
                    $.get('{{ route('quiz.test.status', $test->getRouteKey()) }}').done(function(response) {
                        if (response.time_remaining > 0) {
                            timeRemaining = response.time_remaining;
                            startTimer();
                        } else {
                            autoSubmitQuiz();
                        }
                    });
                }
            });
        </script>
    </body>
</html>
