@extends('layouts.administration.app')

@section('meta_tags')
    {{--  External META's  --}}
@endsection

@section('page_title', __('Create Quiz Test'))

@section('css_links')
    {{--  External CSS  --}}
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/select2/select2.css') }}" />
    <link rel="stylesheet" href="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css') }}" />
@endsection

@section('custom_css')
    {{--  External CSS  --}}
    <style>
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        .quiz-form .form-label {
            font-weight: 600;
        }

        .quiz-form .required::after {
            content: " *";
            color: red;
        }

        .question-selection-container {
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #d9dee3;
            border-radius: 0.375rem;
            padding: 1rem;
            background-color: #f8f9fa;
        }

        .question-item {
            border: 1px solid #e3e6f0;
            border-radius: 0.375rem;
            padding: 0.75rem;
            margin-bottom: 0.5rem;
            background-color: white;
            transition: all 0.3s ease;
        }

        .question-item:hover {
            border-color: #696cff;
            box-shadow: 0 2px 4px rgba(105, 108, 255, 0.1);
        }

        .question-item.selected {
            border-color: #696cff;
            background-color: #f8f9ff;
        }

        .question-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .auto-select-info {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
@endsection

@section('page_name')
    <b class="text-uppercase">{{ __('Create Quiz Test') }}</b>
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item">{{ __('Quiz') }}</li>
    <li class="breadcrumb-item">
        <a href="{{ route('administration.quiz.test.index') }}">{{ __('All Tests') }}</a>
    </li>
    <li class="breadcrumb-item active">{{ __('Create Test') }}</li>
@endsection

@section('content')

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0">{{ __('Create New Quiz Test') }}</h5>
                <div class="card-header-elements ms-auto">
                    <a href="{{ route('administration.quiz.test.index') }}" class="btn btn-sm btn-outline-secondary">
                        <span class="tf-icon ti ti-arrow-left ti-xs me-1"></span>
                        {{ __('Back to Tests') }}
                    </a>
                </div>
            </div>

            <div class="card-body quiz-form">
                <form action="{{ route('administration.quiz.test.store') }}" method="POST" id="quizTestForm" autocomplete="off">
                    @csrf

                    <!-- Candidate Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold">{{ __('Candidate Information') }}</h6>
                            <hr class="mt-0">
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="candidate_name" class="form-label required">{{ __('Candidate Name') }}</label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text"><i class="ti ti-user"></i></span>
                                <input type="text" id="candidate_name" name="candidate_name"
                                       value="{{ old('candidate_name') }}"
                                       placeholder="{{ __('Enter candidate full name') }}"
                                       class="form-control @error('candidate_name') is-invalid @enderror"
                                       required autofocus />
                            </div>
                            @error('candidate_name')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="candidate_email" class="form-label required">{{ __('Candidate Email') }}</label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text"><i class="ti ti-mail"></i></span>
                                <input type="email" id="candidate_email" name="candidate_email"
                                       value="{{ old('candidate_email') }}"
                                       placeholder="{{ __('Enter candidate email address') }}"
                                       class="form-control @error('candidate_email') is-invalid @enderror"
                                       required />
                            </div>
                            @error('candidate_email')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                    </div>

                    <!-- Test Configuration Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold">{{ __('Test Configuration') }}</h6>
                            <hr class="mt-0">
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="total_questions" class="form-label required">{{ __('Total Questions') }}</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-list-numbers"></i></span>
                                <input type="number" id="total_questions" name="total_questions"
                                       value="{{ old('total_questions', 10) }}"
                                       min="1" max="100" step="1"
                                       class="form-control @error('total_questions') is-invalid @enderror"
                                       required />
                            </div>
                            <div class="form-text">{{ __('Number of questions for this test (1-100)') }}</div>
                            @error('total_questions')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="total_time" class="form-label required">{{ __('Time Limit (Minutes)') }}</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-clock"></i></span>
                                <input type="number" id="total_time" name="total_time"
                                       value="{{ old('total_time', 10) }}"
                                       min="1" max="300" step="1"
                                       class="form-control @error('total_time') is-invalid @enderror"
                                       required />
                            </div>
                            <div class="form-text">{{ __('Time limit in minutes (1-300)') }}</div>
                            @error('total_time')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="passing_score" class="form-label required">{{ __('Passing Score') }}</label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-target"></i></span>
                                <input type="number" id="passing_score" name="passing_score"
                                       value="{{ old('passing_score', 6) }}"
                                       min="1" step="1"
                                       class="form-control @error('passing_score') is-invalid @enderror"
                                       required />
                            </div>
                            <div class="form-text">{{ __('Minimum score required to pass') }}</div>
                            @error('passing_score')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                    </div>

                    <!-- Question Selection Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold">{{ __('Question Selection') }}</h6>
                            <hr class="mt-0">
                        </div>

                        <div class="col-12">
                            <div class="auto-select-info">
                                <div class="d-flex align-items-center">
                                    <i class="ti ti-info-circle text-info me-2"></i>
                                    <div>
                                        <strong>{{ __('Auto Selection Mode') }}</strong>
                                        <p class="mb-0 text-muted">{{ __('Leave questions unselected to automatically choose random questions from the active question bank. Or manually select specific questions below.') }}</p>
                                    </div>
                                </div>
                            </div>

                            <div class="d-flex justify-content-between align-items-center mb-3">
                                <div>
                                    <label class="form-label">{{ __('Select Questions (Optional)') }}</label>
                                    <div class="question-counter">
                                        <span id="selected-count">0</span> {{ __('of') }} {{ $questions->count() }} {{ __('questions selected') }}
                                    </div>
                                </div>
                                <div>
                                    <button type="button" class="btn btn-sm btn-outline-primary" id="select-all-btn">
                                        <i class="ti ti-check-all me-1"></i>{{ __('Select All') }}
                                    </button>
                                    <button type="button" class="btn btn-sm btn-outline-secondary" id="clear-all-btn">
                                        <i class="ti ti-x me-1"></i>{{ __('Clear All') }}
                                    </button>
                                </div>
                            </div>

                            @if($questions->count() > 0)
                                <div class="question-selection-container">
                                    @foreach($questions as $question)
                                        <div class="question-item" data-question-id="{{ $question->id }}">
                                            <div class="form-check">
                                                <input class="form-check-input question-checkbox"
                                                       type="checkbox"
                                                       name="question_ids[]"
                                                       value="{{ $question->id }}"
                                                       id="question_{{ $question->id }}"
                                                       {{ in_array($question->id, old('question_ids', [])) ? 'checked' : '' }}>
                                                <label class="form-check-label w-100" for="question_{{ $question->id }}">
                                                    <div class="d-flex justify-content-between align-items-start">
                                                        <div class="flex-grow-1">
                                                            <strong class="text-primary">Q{{ $loop->iteration }}:</strong>
                                                            <span class="ms-2">{{ Str::limit($question->question, 120) }}</span>
                                                        </div>
                                                        <span class="badge bg-light text-dark ms-2">{{ $question->correct_option }}</span>
                                                    </div>
                                                    <div class="mt-2 text-muted small">
                                                        <strong>Options:</strong>
                                                        A) {{ Str::limit($question->option_a, 30) }},
                                                        B) {{ Str::limit($question->option_b, 30) }},
                                                        C) {{ Str::limit($question->option_c, 30) }},
                                                        D) {{ Str::limit($question->option_d, 30) }}
                                                    </div>
                                                </label>
                                            </div>
                                        </div>
                                    @endforeach
                                </div>
                            @else
                                <div class="alert alert-warning">
                                    <i class="ti ti-alert-triangle me-2"></i>
                                    {{ __('No active questions available. Please add questions first.') }}
                                    <a href="{{ route('administration.quiz.question.create') }}" class="btn btn-sm btn-warning ms-2">
                                        <i class="ti ti-plus me-1"></i>{{ __('Add Questions') }}
                                    </a>
                                </div>
                            @endif

                            @error('question_ids')
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i>{{ $message }}</b>
                            @enderror
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="reset" class="btn btn-outline-secondary" onclick="return confirm('{{ __('Are you sure you want to reset the form?') }}')">
                                    <i class="ti ti-refresh me-1"></i>{{ __('Reset Form') }}
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-device-floppy me-1"></i>{{ __('Create Test') }}
                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

@endsection

@section('script_links')
    {{--  External Javascript Links --}}
    <script src="{{ asset('assets/js/form-layouts.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/select2/select2.js') }}"></script>
    <script src="{{ asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js') }}"></script>
@endsection

@section('custom_script')
    {{--  External Custom Javascript  --}}
    <script>
        $(document).ready(function() {
            // Initialize Select2
            $('.select2').select2({
                placeholder: 'Select value',
                allowClear: true
            });

            // Question selection functionality
            let selectedCount = 0;
            const totalQuestions = {{ $questions->count() }};

            // Update selected count
            function updateSelectedCount() {
                selectedCount = $('.question-checkbox:checked').length;
                $('#selected-count').text(selectedCount);

                // Update question items visual state
                $('.question-item').each(function() {
                    const checkbox = $(this).find('.question-checkbox');
                    if (checkbox.is(':checked')) {
                        $(this).addClass('selected');
                    } else {
                        $(this).removeClass('selected');
                    }
                });
            }

            // Question checkbox change handler
            $('.question-checkbox').on('change', function() {
                updateSelectedCount();
            });

            // Select all button
            $('#select-all-btn').on('click', function() {
                $('.question-checkbox').prop('checked', true);
                updateSelectedCount();
            });

            // Clear all button
            $('#clear-all-btn').on('click', function() {
                $('.question-checkbox').prop('checked', false);
                updateSelectedCount();
            });

            // Question item click handler (for better UX)
            $('.question-item').on('click', function(e) {
                if (e.target.type !== 'checkbox') {
                    const checkbox = $(this).find('.question-checkbox');
                    checkbox.prop('checked', !checkbox.prop('checked'));
                    updateSelectedCount();
                }
            });

            // Update passing score max value when total questions changes
            $('#total_questions').on('input', function() {
                const totalQuestions = parseInt($(this).val()) || 1;
                $('#passing_score').attr('max', totalQuestions);

                // Adjust passing score if it exceeds total questions
                const currentPassingScore = parseInt($('#passing_score').val()) || 0;
                if (currentPassingScore > totalQuestions) {
                    $('#passing_score').val(totalQuestions);
                }
            });

            // Validate passing score doesn't exceed total questions
            $('#passing_score').on('input', function() {
                const totalQuestions = parseInt($('#total_questions').val()) || 1;
                const passingScore = parseInt($(this).val()) || 0;

                if (passingScore > totalQuestions) {
                    $(this).val(totalQuestions);
                    alert('{{ __('Passing score cannot be greater than total questions.') }}');
                }
            });

            // Form validation before submit
            $('#quizTestForm').on('submit', function(e) {
                const totalQuestions = parseInt($('#total_questions').val()) || 0;
                const passingScore = parseInt($('#passing_score').val()) || 0;
                const selectedQuestions = $('.question-checkbox:checked').length;

                // Check if we have enough questions
                if (selectedQuestions > 0 && selectedQuestions < totalQuestions) {
                    if (!confirm('{{ __('You have selected fewer questions than the total questions count. The remaining questions will be randomly selected. Continue?') }}')) {
                        e.preventDefault();
                        return false;
                    }
                }

                // Check if no questions are available for auto-selection
                if (selectedQuestions === 0 && totalQuestions > {{ $questions->count() }}) {
                    alert('{{ __('Not enough questions available. Please reduce the total questions count or add more questions.') }}');
                    e.preventDefault();
                    return false;
                }

                // Validate passing score
                if (passingScore > totalQuestions) {
                    alert('{{ __('Passing score cannot be greater than total questions.') }}');
                    e.preventDefault();
                    return false;
                }
            });

            // Set initial max value for passing score and update count
            $('#total_questions').trigger('input');
            updateSelectedCount();
        });
    </script>
@endsection
