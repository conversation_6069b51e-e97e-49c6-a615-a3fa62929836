{"__meta": {"id": "01JXWG2B5CWM3BNTZW1VR4VNF0", "datetime": "2025-06-16 19:50:09", "utime": **********.582323, "method": "GET", "uri": "/application/quiz/test", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750081808.479289, "end": **********.582367, "duration": 1.1030778884887695, "duration_str": "1.1s", "measures": [{"label": "Booting", "start": 1750081808.479289, "relative_start": 0, "end": **********.432047, "relative_end": **********.432047, "duration": 0.****************, "duration_str": "953ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.432068, "relative_start": 0.****************, "end": **********.582373, "relative_end": 5.9604644775390625e-06, "duration": 0.*****************, "duration_str": "150ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.460852, "relative_start": 0.****************, "end": **********.468315, "relative_end": **********.468315, "duration": 0.007462978363037109, "duration_str": "7.46ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.553131, "relative_start": 1.****************, "end": **********.574537, "relative_end": **********.574537, "duration": 0.021405935287475586, "duration_str": "21.41ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x public.quiz.register", "param_count": null, "params": [], "start": **********.557885, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/register.blade.phppublic.quiz.register", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fpublic%2Fquiz%2Fregister.blade.php&line=1", "ajax": false, "filename": "register.blade.php", "line": "?"}, "render_count": 1, "name_original": "public.quiz.register"}, {"name": "1x layouts.public.app", "param_count": null, "params": [], "start": **********.571477, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/public/app.blade.phplayouts.public.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fpublic%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.public.app"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.573011, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET application/quiz/test", "middleware": "web", "controller": "App\\Http\\Controllers\\PublicQuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.register", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:19-40</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00649, "accumulated_duration_str": "6.49ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `testid` = 'SIQT20250616ZNF4' and `status` != 'Completed' and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["SIQT20250616ZNF4", "Completed"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.539055, "duration": 0.00649, "duration_str": "6.49ms", "memory": 0, "memory_str": null, "filename": "PublicQuizController.php:31", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=31", "ajax": false, "filename": "PublicQuizController.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.register", "controller_action": "App\\Http\\Controllers\\PublicQuizController@index", "uri": "GET application/quiz/test", "controller": "App\\Http\\Controllers\\PublicQuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:19-40</a>", "middleware": "web, web", "duration": "1.1s", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-315315216 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-315315216\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-890395346 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-890395346\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:16</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1379 characters\">quiz_test_id=eyJpdiI6Im9zVmJzdStySDlOY2FaZ1ZEaUYxMlE9PSIsInZhbHVlIjoicXh2RkgxRURsalZ4eE9pQVQySnNST0cyQzdWME1uLytzQ0NzSVB0YmhNRXo4WjlFVVFBSmRRc29QUFVReDRWVlJGS0ZKYmNpVFBwbXoxUGJ6NVcydlE9PSIsIm1hYyI6ImUxZDY1ZmEwZjlhYjVmZDljZjNhZTNmZmRjNGM2MDk2MDM0ZGRlMDY4Yzc3ZWEwNTMzOGZmYWU2M2M4YmEwYzIiLCJ0YWciOiIifQ%3D%3D; laravel_session=eyJpdiI6Ik5QSUhCRE1Ka2JwaDByYTVVcmRZVkE9PSIsInZhbHVlIjoiZ0I0Y05sbzRzbW8yeFNhZ0NFWjJ3aXYzOUkwTzlDcmFvUVBZbTJSaDJUODVFUndkM0NnMHpWd3d4d3VJS2QwMjNmRVZpaUYrbUsrcXVPRkxrMGttcHF1TmYxc1JHdzc1K21MeE5PK1RUWmJnTzNEM1dSdklkc0lsQWFudWZWbzYiLCJtYWMiOiI0OTdmMTM1MWM3YzI2ZDI1ODVkNWFlNzEyZGNkZWUxZTE1N2IyYTU4NzVjZjYzMTVkMjRjYTcyMDcxNWMxOTI3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IkE5aWtyZnNNVmhWVksxT1liaUFmbUE9PSIsInZhbHVlIjoiR1JKdEIyNHpEaU14VmZqTHd0ZytGRXNPSEdRYmpncVp4VzU0QXNkcGlCKzh4RXErM3RsOU54QlpENmFDcDJaMXYzQ25kS0tjbHJYNzN5M2FvV3QydCtNa2VycjloeW83ZjBSV1ZQT2kyWmNFamR1Q2Z1UnVScjI5QWlNYWRHZUUiLCJtYWMiOiJjYWJiYWM0YTVhNzEzMDNlYzFlMmVkYjgxMmEwYTFhODg2YjcwZmM4MzQzN2NmMjVlYjZkYTk1YjNjNTYzMDBmIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6Ikg4cW4zeEJlcXhrVnloTUlEMi9SYXc9PSIsInZhbHVlIjoiNjIrSmxKcTJWSnpRU1lDOWRZUEZZNW9NYkU2L0hIdTBDQjlJMmhqU29wa3lRbFJtOUhud2ExRkEyOFp4aUdPMm5DdlArVnlMcHl1ZzZZR3gwWStORUJFWDR3aWpkYU1aYm9IbjJrWjVoZWhNR2ljSVp4MGNVK2tacE1KS2l2R1UiLCJtYWMiOiI4NmU5NzljNjY2ZDc2YTVkZTBiYTFlNDdiNTZhN2QyNTY5Yzg4ODE4NmQxYmY3N2QyMGQyMzdjZjYzYTYwOGQ4IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-13954056 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>quiz_test_id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zOgYDYsZpjT8OLuisTWWHn0ROfzVy1aTCyMaZh59</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AFMC64tXglhKprsQQSyIQPheMvMySaThtMI01Ous</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-13954056\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-177031992 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 13:50:09 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-177031992\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-810326258 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-810326258\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.register", "controller_action": "App\\Http\\Controllers\\PublicQuizController@index"}, "badge": null}}