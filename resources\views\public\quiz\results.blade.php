<!DOCTYPE html>

<html lang="{{ str_replace('_', '-', app()->getLocale()) }}" class="light-style layout-wide customizer-hide" dir="ltr" data-theme="theme-default" data-assets-path="../../assets/" data-template="">
    <head>
        <meta charset="utf-8" />
        <!-- CSRF Token -->
        <meta name="csrf-token" content="{{ csrf_token() }}" />
        <meta http-equiv="X-UA-Compatible" content="IE=edge" />
        <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no, minimum-scale=1.0, maximum-scale=1.0" />

        <title>{{ config('app.name') }} || {{ __('QUIZ RESULTS') }}</title>

        <meta name="description" content="Blue Orange Web Application | Staff-India" />

        <!-- Favicon -->
        <link rel="icon" type="image/x-icon" href="{{ asset(config('app.favicon')) }}" />

        <!-- Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com" />
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin />
        <link href="https://fonts.googleapis.com/css2?family=Public+Sans:ital,wght@0,300;0,400;0,500;0,600;0,700;1,300;1,400;1,500;1,600;1,700&ampdisplay=swap" rel="stylesheet" />

        <!-- Icons -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/fontawesome.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/tabler-icons.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/fonts/flag-icons.css') }}" />

        <!-- Core CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/rtl/core.css') }}" class="template-customizer-core-css" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/rtl/theme-default.css') }}" class="template-customizer-theme-css" />
        <link rel="stylesheet" href="{{ asset('assets/css/demo.css') }}" />

        <!-- Vendors CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/node-waves/node-waves.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.css') }}" />
        <link rel="stylesheet" href="{{ asset('assets/vendor/libs/typeahead-js/typeahead.css') }}" />

        <!-- Page CSS -->
        <link rel="stylesheet" href="{{ asset('assets/vendor/css/pages/page-auth.css') }}" />

        <!-- Helpers -->
        <script src="{{ asset('assets/vendor/js/helpers.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/template-customizer.js') }}"></script>
        <script src="{{ asset('assets/js/config.js') }}"></script>

        <style>
            .results-container {
                max-width: 900px;
                margin: 0 auto;
                padding: 20px;
            }
            
            .results-header {
                background: linear-gradient(135deg, {{ $passed ? '#28a745' : '#dc3545' }} 0%, {{ $passed ? '#20c997' : '#e74c3c' }} 100%);
                color: white;
                padding: 30px;
                border-radius: 15px;
                margin-bottom: 30px;
                text-align: center;
            }
            
            .score-circle {
                width: 120px;
                height: 120px;
                border-radius: 50%;
                background: rgba(255,255,255,0.2);
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 20px;
                font-size: 2rem;
                font-weight: bold;
            }
            
            .question-review {
                border: 1px solid #e3e6f0;
                border-radius: 10px;
                padding: 20px;
                margin-bottom: 20px;
                background: white;
            }
            
            .question-review.correct {
                border-left: 5px solid #28a745;
                background-color: #f8fff9;
            }
            
            .question-review.incorrect {
                border-left: 5px solid #dc3545;
                background-color: #fff8f8;
            }
            
            .question-review.unanswered {
                border-left: 5px solid #ffc107;
                background-color: #fffdf5;
            }
            
            .option-review {
                padding: 10px 15px;
                margin: 5px 0;
                border-radius: 5px;
                border: 1px solid #e3e6f0;
            }
            
            .option-review.correct {
                background-color: #d4edda;
                border-color: #c3e6cb;
                color: #155724;
            }
            
            .option-review.selected-wrong {
                background-color: #f8d7da;
                border-color: #f5c6cb;
                color: #721c24;
            }
            
            .option-review.selected-correct {
                background-color: #d4edda;
                border-color: #c3e6cb;
                color: #155724;
                font-weight: bold;
            }
        </style>
    </head>

    <body>
        <!-- Content -->
        <div class="results-container">
            <!-- Results Header -->
            <div class="results-header">
                <div class="score-circle">
                    {{ $test->total_score }}/{{ $test->total_questions }}
                </div>
                <h2 class="mb-2">
                    @if($passed)
                        <i class="ti ti-check-circle me-2"></i>Congratulations!
                    @else
                        <i class="ti ti-x-circle me-2"></i>Better Luck Next Time
                    @endif
                </h2>
                <h5 class="mb-3">{{ $test->candidate_name }}</h5>
                <p class="mb-2">
                    You scored <strong>{{ $test->total_score }} out of {{ $test->total_questions }}</strong> ({{ $percentage }}%)
                </p>
                <p class="mb-0">
                    @if($passed)
                        You have successfully passed the quiz! 🎉
                    @else
                        You needed {{ $test->passing_score }} to pass. Keep practicing! 💪
                    @endif
                </p>
            </div>

            <!-- Test Summary -->
            <div class="row mb-4">
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="ti ti-list-numbers text-primary mb-2" style="font-size: 2rem;"></i>
                            <h6 class="card-title">Total Questions</h6>
                            <h4 class="text-primary">{{ $test->total_questions }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="ti ti-check text-success mb-2" style="font-size: 2rem;"></i>
                            <h6 class="card-title">Correct</h6>
                            <h4 class="text-success">{{ $test->total_score }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="ti ti-x text-danger mb-2" style="font-size: 2rem;"></i>
                            <h6 class="card-title">Incorrect</h6>
                            <h4 class="text-danger">{{ $test->attempted_questions - $test->total_score }}</h4>
                        </div>
                    </div>
                </div>
                <div class="col-md-3">
                    <div class="card text-center">
                        <div class="card-body">
                            <i class="ti ti-clock text-warning mb-2" style="font-size: 2rem;"></i>
                            <h6 class="card-title">Time Taken</h6>
                            <h4 class="text-warning">
                                @if($test->started_at && $test->ended_at)
                                    {{ $test->started_at->diffInMinutes($test->ended_at) }}m
                                @else
                                    --
                                @endif
                            </h4>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Question Review -->
            <div class="card">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ti ti-eye me-2"></i>Question Review
                    </h5>
                </div>
                <div class="card-body">
                    @foreach($questions as $index => $question)
                        @php
                            $userAnswer = $test->answers->where('quiz_question_id', $question->id)->first();
                            $isCorrect = $userAnswer && $userAnswer->is_correct;
                            $isAnswered = $userAnswer !== null;
                        @endphp
                        
                        <div class="question-review {{ $isAnswered ? ($isCorrect ? 'correct' : 'incorrect') : 'unanswered' }}">
                            <div class="d-flex justify-content-between align-items-start mb-3">
                                <h6 class="mb-0">
                                    <span class="badge bg-primary me-2">Q{{ $index + 1 }}</span>
                                    {{ $question->question }}
                                </h6>
                                <div class="text-end">
                                    @if($isAnswered)
                                        @if($isCorrect)
                                            <span class="badge bg-success">
                                                <i class="ti ti-check"></i> Correct
                                            </span>
                                        @else
                                            <span class="badge bg-danger">
                                                <i class="ti ti-x"></i> Incorrect
                                            </span>
                                        @endif
                                    @else
                                        <span class="badge bg-warning">
                                            <i class="ti ti-minus"></i> Not Answered
                                        </span>
                                    @endif
                                </div>
                            </div>
                            
                            <div class="options">
                                @foreach(['A', 'B', 'C', 'D'] as $option)
                                    @php
                                        $isUserSelected = $userAnswer && $userAnswer->selected_option === $option;
                                        $isCorrectOption = $question->correct_option === $option;
                                    @endphp
                                    
                                    <div class="option-review 
                                        @if($isCorrectOption) correct @endif
                                        @if($isUserSelected && $isCorrectOption) selected-correct @endif
                                        @if($isUserSelected && !$isCorrectOption) selected-wrong @endif
                                    ">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <strong>{{ $option }}.</strong> {{ $question->{'option_' . strtolower($option)} }}
                                            </div>
                                            <div>
                                                @if($isCorrectOption)
                                                    <i class="ti ti-check text-success"></i>
                                                @endif
                                                @if($isUserSelected)
                                                    <i class="ti ti-user text-primary"></i>
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                @endforeach
                            </div>
                        </div>
                    @endforeach
                </div>
            </div>

            <!-- Test Details -->
            <div class="card mt-4">
                <div class="card-header">
                    <h5 class="mb-0">
                        <i class="ti ti-info-circle me-2"></i>Test Details
                    </h5>
                </div>
                <div class="card-body">
                    <div class="row">
                        <div class="col-md-6">
                            <p><strong>Test ID:</strong> {{ $test->getRouteKey() }}</p>
                            <p><strong>Candidate:</strong> {{ $test->candidate_name }}</p>
                            <p><strong>Email:</strong> {{ $test->candidate_email }}</p>
                        </div>
                        <div class="col-md-6">
                            <p><strong>Started At:</strong> {{ $test->started_at?->format('M d, Y h:i A') ?? 'N/A' }}</p>
                            <p><strong>Completed At:</strong> {{ $test->ended_at?->format('M d, Y h:i A') ?? 'N/A' }}</p>
                            <p><strong>Auto Submitted:</strong> {{ $test->auto_submitted ? 'Yes (Time Expired)' : 'No' }}</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Actions -->
            <div class="text-center mt-4">
                <a href="{{ url('/') }}" class="btn btn-outline-primary">
                    <i class="ti ti-home me-2"></i>Go to Homepage
                </a>
                <button class="btn btn-outline-secondary" onclick="window.print()">
                    <i class="ti ti-printer me-2"></i>Print Results
                </button>
            </div>
        </div>

        <!-- Core JS -->
        <script src="{{ asset('assets/vendor/libs/jquery/jquery.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/popper/popper.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/bootstrap.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/node-waves/node-waves.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/perfect-scrollbar/perfect-scrollbar.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/hammer/hammer.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/i18n/i18n.js') }}"></script>
        <script src="{{ asset('assets/vendor/libs/typeahead-js/typeahead.js') }}"></script>
        <script src="{{ asset('assets/vendor/js/menu.js') }}"></script>
        <script src="{{ asset('assets/js/main.js') }}"></script>

        @include('sweetalert::alert')
    </body>
</html>
