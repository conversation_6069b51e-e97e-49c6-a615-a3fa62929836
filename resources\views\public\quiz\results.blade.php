@extends('layouts.public.quiz')

@section('page_title', __('QUIZ RESULTS'))

@section('custom_css')
    <style>
        .results-header {
            background: linear-gradient(135deg, {{ $passed ? '#28a745' : '#dc3545' }} 0%, {{ $passed ? '#20c997' : '#e74c3c' }} 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba({{ $passed ? '40, 167, 69' : '220, 53, 69' }}, 0.3);
        }

        .score-circle {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2.5rem;
            font-weight: bold;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .question-review {
            border: 1px solid #e3e6f0;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .question-review:hover {
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .question-review.correct {
            border-left: 6px solid #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
        }

        .question-review.incorrect {
            border-left: 6px solid #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #ffffff 100%);
        }

        .question-review.unanswered {
            border-left: 6px solid #ffc107;
            background: linear-gradient(135deg, #fffdf5 0%, #ffffff 100%);
        }

        .option-review {
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 10px;
            border: 2px solid #e3e6f0;
            transition: all 0.3s ease;
        }

        .option-review.correct {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            border-color: #c3e6cb;
            color: #155724;
            font-weight: 600;
        }

        .option-review.selected-wrong {
            background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
            border-color: #f5c6cb;
            color: #721c24;
            font-weight: 600;
        }

        .option-review.selected-correct {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
            border-width: 3px;
        }

        .stats-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            border: 2px solid #696cff;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(105, 108, 255, 0.15);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .results-header {
                background: {{ $passed ? '#28a745' : '#dc3545' }} !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
@endsection

@section('content')
    <!-- Results Header -->
    <div class="results-header">
        <div class="score-circle">
            {{ $test->total_score }}/{{ $test->total_questions }}
        </div>
        <h2 class="mb-3">
            @if($passed)
                <i class="ti ti-trophy me-2"></i>Congratulations!
            @else
                <i class="ti ti-x-circle me-2"></i>Better Luck Next Time
            @endif
        </h2>
        <h4 class="mb-3"><i class="ti ti-user me-2"></i>{{ $test->candidate_name }}</h4>
        <div class="row">
            <div class="col-md-6">
                <p class="mb-2">
                    <i class="ti ti-chart-pie me-2"></i>Score: <strong>{{ $test->total_score }} out of {{ $test->total_questions }}</strong>
                </p>
                <p class="mb-2">
                    <i class="ti ti-percentage me-2"></i>Percentage: <strong>{{ $percentage }}%</strong>
                </p>
            </div>
            <div class="col-md-6">
                <p class="mb-2">
                    <i class="ti ti-target me-2"></i>Required: <strong>{{ $test->passing_score }} out of {{ $test->total_questions }}</strong>
                </p>
                <p class="mb-2">
                    <i class="ti ti-id me-2"></i>Test ID: <strong>{{ $test->getRouteKey() }}</strong>
                </p>
            </div>
        </div>
        <hr style="border-color: rgba(255,255,255,0.3); margin: 20px 0;">
        <h5 class="mb-0">
            @if($passed)
                <i class="ti ti-check-circle me-2"></i>You have successfully passed the quiz! 🎉
            @else
                <i class="ti ti-alert-triangle me-2"></i>You needed {{ $test->passing_score }} to pass. Keep practicing! 💪
            @endif
        </h5>
    </div>

    <!-- Test Summary -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-list-numbers text-primary mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Total Questions</h6>
                    <h3 class="text-primary mb-0">{{ $test->total_questions }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-check text-success mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Correct Answers</h6>
                    <h3 class="text-success mb-0">{{ $test->total_score }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-x text-danger mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Incorrect Answers</h6>
                    <h3 class="text-danger mb-0">{{ $test->attempted_questions - $test->total_score }}</h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-clock text-warning mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Time Taken</h6>
                    <h3 class="text-warning mb-0">
                        @if($test->started_at && $test->ended_at)
                            {{ $test->started_at->diffInMinutes($test->ended_at) }}m
                        @else
                            --
                        @endif
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Question Review -->
    <div class="card">
        <div class="card-header header-elements">
            <h5 class="mb-0">
                <i class="ti ti-eye me-2"></i>Detailed Question Review
            </h5>
            <div class="card-header-elements ms-auto">
                <span class="badge bg-primary">{{ $questions->count() }} Questions</span>
            </div>
        </div>
        <div class="card-body">
            @foreach($questions as $index => $question)
                @php
                    $userAnswer = $test->answers->where('quiz_question_id', $question->id)->first();
                    $isCorrect = $userAnswer && $userAnswer->is_correct;
                    $isAnswered = $userAnswer !== null;
                @endphp

                <div class="question-review {{ $isAnswered ? ($isCorrect ? 'correct' : 'incorrect') : 'unanswered' }}">
                    <div class="d-flex justify-content-between align-items-start mb-4">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-primary me-3" style="font-size: 0.9rem;">Q{{ $index + 1 }}</span>
                                @if($isAnswered)
                                    @if($isCorrect)
                                        <span class="badge bg-success">
                                            <i class="ti ti-check me-1"></i>Correct
                                        </span>
                                    @else
                                        <span class="badge bg-danger">
                                            <i class="ti ti-x me-1"></i>Incorrect
                                        </span>
                                    @endif
                                @else
                                    <span class="badge bg-warning">
                                        <i class="ti ti-minus me-1"></i>Not Answered
                                    </span>
                                @endif
                            </div>
                            <h6 class="mb-0">{{ $question->question }}</h6>
                        </div>
                    </div>

                    <div class="options">
                        @foreach(['A', 'B', 'C', 'D'] as $option)
                            @php
                                $isUserSelected = $userAnswer && $userAnswer->selected_option === $option;
                                $isCorrectOption = $question->correct_option === $option;
                            @endphp

                            <div class="option-review
                                @if($isCorrectOption) correct @endif
                                @if($isUserSelected && $isCorrectOption) selected-correct @endif
                                @if($isUserSelected && !$isCorrectOption) selected-wrong @endif
                            ">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="flex-grow-1">
                                        <strong>{{ $option }}.</strong> {{ $question->{'option_' . strtolower($option)} }}
                                    </div>
                                    <div class="d-flex gap-2">
                                        @if($isCorrectOption)
                                            <span class="badge bg-success-subtle text-success">
                                                <i class="ti ti-check"></i> Correct
                                            </span>
                                        @endif
                                        @if($isUserSelected)
                                            <span class="badge bg-primary-subtle text-primary">
                                                <i class="ti ti-user"></i> Your Answer
                                            </span>
                                        @endif
                                    </div>
                                </div>
                            </div>
                        @endforeach
                    </div>
                </div>
            @endforeach
        </div>
    </div>

    <!-- Test Details -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="ti ti-info-circle me-2"></i>Test Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label text-muted">Test ID</label>
                        <p class="fw-semibold">{{ $test->getRouteKey() }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Candidate Name</label>
                        <p class="fw-semibold">{{ $test->candidate_name }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email Address</label>
                        <p class="fw-semibold">{{ $test->candidate_email }}</p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label text-muted">Started At</label>
                        <p class="fw-semibold">{{ $test->started_at?->format('M d, Y h:i A') ?? 'N/A' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Completed At</label>
                        <p class="fw-semibold">{{ $test->ended_at?->format('M d, Y h:i A') ?? 'N/A' }}</p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Submission Type</label>
                        <p class="fw-semibold">
                            @if($test->auto_submitted)
                                <span class="badge bg-warning">
                                    <i class="ti ti-clock me-1"></i>Auto Submitted (Time Expired)
                                </span>
                            @else
                                <span class="badge bg-success">
                                    <i class="ti ti-check me-1"></i>Manual Submission
                                </span>
                            @endif
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="text-center mt-4 no-print">
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="{{ url('/') }}" class="btn btn-outline-primary">
                <i class="ti ti-home me-2"></i>Go to Homepage
            </a>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="ti ti-printer me-2"></i>Print Results
            </button>
            <button class="btn btn-outline-info" onclick="shareResults()">
                <i class="ti ti-share me-2"></i>Share Results
            </button>
        </div>
    </div>
@endsection

@section('custom_script')
    <script>
        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: 'Quiz Results - {{ $test->candidate_name }}',
                    text: 'I scored {{ $test->total_score }}/{{ $test->total_questions }} ({{ $percentage }}%) on the quiz!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Results URL copied to clipboard!');
                });
            }
        }

        // Print optimization
        window.addEventListener('beforeprint', function() {
            document.title = 'Quiz Results - {{ $test->candidate_name }} - {{ $test->getRouteKey() }}';
        });
    </script>
@endsection
