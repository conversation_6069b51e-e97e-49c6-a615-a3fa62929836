{"__meta": {"id": "01JXWF4JGSYJV360VY6YGQSKE0", "datetime": "2025-06-16 19:33:54", "utime": **********.075425, "method": "GET", "uri": "/application/quiz/test/J9a2D4Q0k56RodvA", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 1, "messages": [{"message": "[19:33:53] LOG.error: Route [quiz.test.start] not defined. {\n    \"view\": {\n        \"view\": \"E:\\\\NIGEL\\\\LaravelProjects\\\\laragon\\\\www\\\\BlueOrange\\\\resources\\\\views\\\\public\\\\quiz\\\\test.blade.php\",\n        \"data\": {\n            \"errors\": \"<pre class=sf-dump id=sf-dump-661169016 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Support\\\\ViewErrorBag<\\/span> {<a class=sf-dump-ref>#927<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">bags<\\/span>: []\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-661169016\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"test\": \"<pre class=sf-dump id=sf-dump-1877049287 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>App\\\\Models\\\\Quiz\\\\QuizTest\\\\QuizTest<\\/span> {<a class=sf-dump-ref>#1943<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"10 characters\\\">quiz_tests<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n  +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:18<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>testid<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">SIQT20250616ZNF4<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>creator_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>candidate_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">Nigel Long<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>candidate_email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>total_questions<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>total_time<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>passing_score<\\/span>\\\" => <span class=sf-dump-num>6<\\/span>\\n    \\\"<span class=sf-dump-key>question_ids<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"33 characters\\\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>attempted_questions<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>total_score<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Pending<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-16 19:33:52<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-16 19:33:52<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:18<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>id<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>testid<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"16 characters\\\">SIQT20250616ZNF4<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>creator_id<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>candidate_name<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">Nigel Long<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>candidate_email<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"14 characters\\\"><EMAIL><\\/span>\\\"\\n    \\\"<span class=sf-dump-key>total_questions<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>total_time<\\/span>\\\" => <span class=sf-dump-num>10<\\/span>\\n    \\\"<span class=sf-dump-key>passing_score<\\/span>\\\" => <span class=sf-dump-num>6<\\/span>\\n    \\\"<span class=sf-dump-key>question_ids<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"33 characters\\\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>attempted_questions<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>total_score<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => <span class=sf-dump-num>0<\\/span>\\n    \\\"<span class=sf-dump-key>status<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">Pending<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>created_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-16 19:33:52<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>updated_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">2025-06-16 19:33:52<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => <span class=sf-dump-const>null<\\/span>\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:5<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    \\\"<span class=sf-dump-key>question_ids<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"5 characters\\\">array<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>started_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>ended_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>auto_submitted<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">boolean<\\/span>\\\"\\n    \\\"<span class=sf-dump-key>deleted_at<\\/span>\\\" => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">datetime<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n  +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n  +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:13<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">creator_id<\\/span>\\\"\\n    <span class=sf-dump-index>1<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">candidate_name<\\/span>\\\"\\n    <span class=sf-dump-index>2<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">candidate_email<\\/span>\\\"\\n    <span class=sf-dump-index>3<\\/span> => \\\"<span class=sf-dump-str title=\\\"15 characters\\\">total_questions<\\/span>\\\"\\n    <span class=sf-dump-index>4<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">total_time<\\/span>\\\"\\n    <span class=sf-dump-index>5<\\/span> => \\\"<span class=sf-dump-str title=\\\"13 characters\\\">passing_score<\\/span>\\\"\\n    <span class=sf-dump-index>6<\\/span> => \\\"<span class=sf-dump-str title=\\\"12 characters\\\">question_ids<\\/span>\\\"\\n    <span class=sf-dump-index>7<\\/span> => \\\"<span class=sf-dump-str title=\\\"10 characters\\\">started_at<\\/span>\\\"\\n    <span class=sf-dump-index>8<\\/span> => \\\"<span class=sf-dump-str title=\\\"8 characters\\\">ended_at<\\/span>\\\"\\n    <span class=sf-dump-index>9<\\/span> => \\\"<span class=sf-dump-str title=\\\"19 characters\\\">attempted_questions<\\/span>\\\"\\n    <span class=sf-dump-index>10<\\/span> => \\\"<span class=sf-dump-str title=\\\"11 characters\\\">total_score<\\/span>\\\"\\n    <span class=sf-dump-index>11<\\/span> => \\\"<span class=sf-dump-str title=\\\"14 characters\\\">auto_submitted<\\/span>\\\"\\n    <span class=sf-dump-index>12<\\/span> => \\\"<span class=sf-dump-str title=\\\"6 characters\\\">status<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str>*<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => \\\"<span class=sf-dump-str title=\\\"7 characters\\\">answers<\\/span>\\\"\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-1877049287\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"questions\": \"<pre class=sf-dump id=sf-dump-873809673 data-indent-pad=\\\"  \\\"><span class=sf-dump-note>Illuminate\\\\Database\\\\Eloquent\\\\Collection<\\/span> {<a class=sf-dump-ref>#1973<\\/a><samp data-depth=1 class=sf-dump-expanded>\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">items<\\/span>: <span class=sf-dump-note>array:10<\\/span> [<samp data-depth=2 class=sf-dump-compact>\\n    <span class=sf-dump-index>0<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1972<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>1<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1970<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>2<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1969<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>3<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1968<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>4<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1967<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>5<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1966<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>6<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1965<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>7<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1964<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>8<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1963<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n    <span class=sf-dump-index>9<\\/span> => <span class=\\\"sf-dump-note sf-dump-ellipsization\\\" title=\\\"App\\\\Models\\\\Quiz\\\\QuizQuestion\\\\QuizQuestion\\n\\\"><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">App\\\\Models\\\\Quiz\\\\QuizQuestion<\\/span><span class=\\\"sf-dump-ellipsis sf-dump-ellipsis-note\\\">\\\\<\\/span><span class=\\\"sf-dump-ellipsis-tail\\\">QuizQuestion<\\/span><\\/span> {<a class=sf-dump-ref>#1962<\\/a><samp data-depth=3 class=sf-dump-compact>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">connection<\\/span>: \\\"<span class=sf-dump-str title=\\\"5 characters\\\">mysql<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">table<\\/span>: \\\"<span class=sf-dump-str title=\\\"14 characters\\\">quiz_questions<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">primaryKey<\\/span>: \\\"<span class=sf-dump-str title=\\\"2 characters\\\">id<\\/span>\\\"\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">keyType<\\/span>: \\\"<span class=sf-dump-str title=\\\"3 characters\\\">int<\\/span>\\\"\\n      +<span class=sf-dump-public title=\\\"Public property\\\">incrementing<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">with<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">withCount<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">preventsLazyLoading<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">perPage<\\/span>: <span class=sf-dump-num>15<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">exists<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">wasRecentlyCreated<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributes<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">original<\\/span>: <span class=sf-dump-note>array:12<\\/span> [ &#8230;12]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">changes<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">casts<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">classCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">attributeCastCache<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dateFormat<\\/span>: <span class=sf-dump-const>null<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">appends<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">dispatchesEvents<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">observables<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">relations<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">touches<\\/span>: []\\n      +<span class=sf-dump-public title=\\\"Public property\\\">timestamps<\\/span>: <span class=sf-dump-const>true<\\/span>\\n      +<span class=sf-dump-public title=\\\"Public property\\\">usesUniqueIds<\\/span>: <span class=sf-dump-const>false<\\/span>\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">hidden<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">visible<\\/span>: []\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">fillable<\\/span>: <span class=sf-dump-note>array:8<\\/span> [ &#8230;8]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">guarded<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">cascadeDeletes<\\/span>: <span class=sf-dump-note>array:1<\\/span> [ &#8230;1]\\n      #<span class=sf-dump-protected title=\\\"Protected property\\\">forceDeleting<\\/span>: <span class=sf-dump-const>false<\\/span>\\n    <\\/samp>}\\n  <\\/samp>]\\n  #<span class=sf-dump-protected title=\\\"Protected property\\\">escapeWhenCastingToString<\\/span>: <span class=sf-dump-const>false<\\/span>\\n<\\/samp>}\\n<\\/pre><script>Sfdump(\\\"sf-dump-873809673\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\",\n            \"existingAnswers\": \"<pre class=sf-dump id=sf-dump-88760988 data-indent-pad=\\\"  \\\">[]\\n<\\/pre><script>Sfdump(\\\"sf-dump-88760988\\\", {\\\"maxDepth\\\":3,\\\"maxStringLength\\\":160})<\\/script>\\n\"\n        }\n    },\n    \"exception\": {}\n}", "message_html": null, "is_string": false, "label": "error", "time": **********.33286, "xdebug_link": null, "collector": "log"}]}, "time": {"start": 1750080832.364304, "end": **********.075469, "duration": 1.711164951324463, "duration_str": "1.71s", "measures": [{"label": "Booting", "start": 1750080832.364304, "relative_start": 0, "end": **********.031657, "relative_end": **********.031657, "duration": 0.****************, "duration_str": "667ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.031678, "relative_start": 0.****************, "end": **********.075473, "relative_end": 4.0531158447265625e-06, "duration": 1.***************, "duration_str": "1.04s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.067357, "relative_start": 0.****************, "end": **********.075309, "relative_end": **********.075309, "duration": 0.007951974868774414, "duration_str": "7.95ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.249181, "relative_start": 0.****************, "end": **********.075492, "relative_end": 2.288818359375e-05, "duration": 0.****************, "duration_str": "826ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "30MB"}, "exceptions": {"count": 2, "exceptions": [{"type": "Illuminate\\View\\ViewException", "message": "Route [quiz.test.start] not defined. (View: E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views\\public\\quiz\\test.blade.php)", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php", "line": 477, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:60</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">[object Symfony\\Component\\Routing\\Exception\\RouteNotFoundException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>60</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">handleViewException</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"67 characters\">[object Symfony\\Component\\Routing\\Exception\\RouteNotFoundException]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>1</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/09eeb4d462694766cabe342addee4cae.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"4 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"4 occurrences\">#927</a><samp data-depth=5 id=sf-dump-**********-ref2927 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"4 occurrences\">#1943</a><samp data-depth=5 id=sf-dump-**********-ref21943 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Nigel Long</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pending</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Nigel Long</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pending</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">question_ids</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"4 occurrences\">#1973</a><samp data-depth=5 id=sf-dump-**********-ref21973 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1972</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1970</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1969</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1968</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Incidunt qui et qua</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Tempore optio rati</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Facere nisi aperiam</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim ab fugiat labor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Labore dolorum volup</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Incidunt qui et qua</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Tempore optio rati</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Facere nisi aperiam</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim ab fugiat labor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Labore dolorum volup</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1967</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Beatae voluptate iru</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Et corrupti digniss</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et adipisicing nobis</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ea aut consequatur</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Sunt voluptas animi</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Beatae voluptate iru</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Et corrupti digniss</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et adipisicing nobis</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ea aut consequatur</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Sunt voluptas animi</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1966</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Debitis commodo labo</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et earum maiores est</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Voluptatem molestia</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Eligendi aut eum rec</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aut ea voluptatum su</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>D</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Debitis commodo labo</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et earum maiores est</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Voluptatem molestia</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Eligendi aut eum rec</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aut ea voluptatum su</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>D</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1965</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cumque est rerum un</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugit anim voluptas</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quod dolore unde qui</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Neque quae aute et e</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolorem autem at obc</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cumque est rerum un</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugit anim voluptas</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quod dolore unde qui</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Neque quae aute et e</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolorem autem at obc</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1964</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor anim commodo q</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Id qui dolore nulla</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Sunt minima eos id</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aspernatur vel dolor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Porro anim amet ani</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor anim commodo q</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Id qui dolore nulla</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Sunt minima eos id</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aspernatur vel dolor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Porro anim amet ani</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1963</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Iure et ut deleniti</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam fugiat ipsam ip</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Similique consequat</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Veniam deleniti har</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eligendi asperiores</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Iure et ut deleniti</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam fugiat ipsam ip</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Similique consequat</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Veniam deleniti har</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eligendi asperiores</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1962</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ad suscipit labore n</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aute officia rem tem</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet quidem culp</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem adipisci dolo</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quis ducimus numqua</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ad suscipit labore n</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aute officia rem tem</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet quidem culp</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem adipisci dolo</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quis ducimus numqua</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>72</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/09eeb4d462694766cabe342addee4cae.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"4 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"4 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"4 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"4 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"90 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/test.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"4 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"4 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"4 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"4 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>207</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"90 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/test.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"4 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"4 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"4 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"4 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"4 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>159</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>918</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">vendor/realrashid/sweet-alert/src/ToSweetAlert.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">RealRashid\\SweetAlert\\ToSweetAlert</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $url;\n", "        }\n", "\n", "        throw new RouteNotFoundException(\"Route [{$name}] not defined.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FUrlGenerator.php&line=477", "ajax": false, "filename": "UrlGenerator.php", "line": "477"}}, {"type": "Symfony\\Component\\Routing\\Exception\\RouteNotFoundException", "message": "Route [quiz.test.start] not defined.", "code": 0, "file": "vendor/laravel/framework/src/Illuminate/Routing/UrlGenerator.php", "line": 477, "stack_trace": null, "stack_trace_html": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:63</span> [<samp data-depth=1 class=sf-dump-expanded>\n  <span class=sf-dump-index>0</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"62 characters\">vendor/laravel/framework/src/Illuminate/Foundation/helpers.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>811</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Routing\\UrlGenerator</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">quiz.test.start</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">J9a2D4Q0k56RodvA</span>\"\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-const>true</span>\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"60 characters\">storage/framework/views/09eeb4d462694766cabe342addee4cae.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>253</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"5 characters\">route</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">quiz.test.start</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">J9a2D4Q0k56RodvA</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:4</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>123</span>\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views\\09eeb4d462694766cabe342addee4cae.php</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"7 characters\">require</span>\"\n  </samp>]\n  <span class=sf-dump-index>3</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"65 characters\">vendor/laravel/framework/src/Illuminate/Filesystem/Filesystem.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>124</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"31 characters\">Illuminate\\Filesystem\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>4</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/PhpEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>58</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">getRequire</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"32 characters\">Illuminate\\Filesystem\\Filesystem</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/09eeb4d462694766cabe342addee4cae.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"5 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"5 occurrences\">#927</a><samp data-depth=5 id=sf-dump-**********-ref2927 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">bags</span>: []\n        </samp>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"5 occurrences\">#1943</a><samp data-depth=5 id=sf-dump-**********-ref21943 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"10 characters\">quiz_tests</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n          #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n          +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n          #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n          +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n          +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Nigel Long</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pending</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:18</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>testid</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n            \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>candidate_name</span>\" => \"<span class=sf-dump-str title=\"10 characters\">Nigel Long</span>\"\n            \"<span class=sf-dump-key>candidate_email</span>\" => \"<span class=sf-dump-str title=\"14 characters\"><EMAIL></span>\"\n            \"<span class=sf-dump-key>total_questions</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>total_time</span>\" => <span class=sf-dump-num>10</span>\n            \"<span class=sf-dump-key>passing_score</span>\" => <span class=sf-dump-num>6</span>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"33 characters\">[9, 7, 8, 6, 12, 10, 5, 3, 11, 4]</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>ended_at</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>attempted_questions</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>total_score</span>\" => <span class=sf-dump-const>null</span>\n            \"<span class=sf-dump-key>auto_submitted</span>\" => <span class=sf-dump-num>0</span>\n            \"<span class=sf-dump-key>status</span>\" => \"<span class=sf-dump-str title=\"7 characters\">Pending</span>\"\n            \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-16 19:33:52</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:5</span> [<samp data-depth=6 class=sf-dump-compact>\n            \"<span class=sf-dump-key>question_ids</span>\" => \"<span class=sf-dump-str title=\"5 characters\">array</span>\"\n            \"<span class=sf-dump-key>started_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>ended_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n            \"<span class=sf-dump-key>auto_submitted</span>\" => \"<span class=sf-dump-str title=\"7 characters\">boolean</span>\"\n            \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n          #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n          +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n          +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n          #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n          #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:13</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n            <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"14 characters\">candidate_name</span>\"\n            <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"15 characters\">candidate_email</span>\"\n            <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"15 characters\">total_questions</span>\"\n            <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"10 characters\">total_time</span>\"\n            <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"13 characters\">passing_score</span>\"\n            <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"12 characters\">question_ids</span>\"\n            <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"10 characters\">started_at</span>\"\n            <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"8 characters\">ended_at</span>\"\n            <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"19 characters\">attempted_questions</span>\"\n            <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"11 characters\">total_score</span>\"\n            <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"14 characters\">auto_submitted</span>\"\n            <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"6 characters\">status</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"5 occurrences\">#1973</a><samp data-depth=5 id=sf-dump-**********-ref21973 class=sf-dump-compact>\n          #<span class=sf-dump-protected title=\"Protected property\">items</span>: <span class=sf-dump-note>array:10</span> [<samp data-depth=6 class=sf-dump-compact>\n            <span class=sf-dump-index>0</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1972</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>3</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Cum reiciendis dolor</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Deleniti debitis cup</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet assumenda c</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Modi dolor illo adip</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Corrupti consequat</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:31:08</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>1</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1970</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>4</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Totam quod exercitat</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam saepe dolore et</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Est dolore atque con</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Nostrud nobis debiti</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem omnis eiusmod</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:38</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>2</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1969</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>5</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Temporibus alias ali</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Rem sint sint quibu</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Libero dolore sit sa</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim suscipit molest</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quas enim ea sint d</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:45</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>3</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1968</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Incidunt qui et qua</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Tempore optio rati</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Facere nisi aperiam</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim ab fugiat labor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Labore dolorum volup</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>6</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Incidunt qui et qua</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Tempore optio rati</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Facere nisi aperiam</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Anim ab fugiat labor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Labore dolorum volup</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:35:51</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>4</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1967</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Beatae voluptate iru</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Et corrupti digniss</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et adipisicing nobis</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ea aut consequatur</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Sunt voluptas animi</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>7</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Beatae voluptate iru</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Et corrupti digniss</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et adipisicing nobis</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Ea aut consequatur</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Sunt voluptas animi</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:02</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>5</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1966</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Debitis commodo labo</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et earum maiores est</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Voluptatem molestia</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Eligendi aut eum rec</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aut ea voluptatum su</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>D</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>8</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Debitis commodo labo</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Et earum maiores est</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Voluptatem molestia</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Eligendi aut eum rec</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aut ea voluptatum su</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>D</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:09</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>6</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1965</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cumque est rerum un</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugit anim voluptas</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quod dolore unde qui</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Neque quae aute et e</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolorem autem at obc</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>9</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Cumque est rerum un</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Fugit anim voluptas</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quod dolore unde qui</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Neque quae aute et e</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolorem autem at obc</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>C</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:20</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>7</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1964</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor anim commodo q</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Id qui dolore nulla</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Sunt minima eos id</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aspernatur vel dolor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Porro anim amet ani</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>10</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Dolor anim commodo q</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Id qui dolore nulla</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"18 characters\">Sunt minima eos id</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aspernatur vel dolor</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Porro anim amet ani</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:26</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>8</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1963</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Iure et ut deleniti</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam fugiat ipsam ip</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Similique consequat</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Veniam deleniti har</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eligendi asperiores</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>11</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Iure et ut deleniti</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Nam fugiat ipsam ip</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Similique consequat</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Veniam deleniti har</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eligendi asperiores</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>B</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:36</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n            <span class=sf-dump-index>9</span> => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizQuestion</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizQuestion</span></span> {<a class=sf-dump-ref>#1962</a><samp data-depth=7 class=sf-dump-compact>\n              #<span class=sf-dump-protected title=\"Protected property\">connection</span>: \"<span class=sf-dump-str title=\"5 characters\">mysql</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">table</span>: \"<span class=sf-dump-str title=\"14 characters\">quiz_questions</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">primaryKey</span>: \"<span class=sf-dump-str title=\"2 characters\">id</span>\"\n              #<span class=sf-dump-protected title=\"Protected property\">keyType</span>: \"<span class=sf-dump-str title=\"3 characters\">int</span>\"\n              +<span class=sf-dump-public title=\"Public property\">incrementing</span>: <span class=sf-dump-const>true</span>\n              #<span class=sf-dump-protected title=\"Protected property\">with</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">withCount</span>: []\n              +<span class=sf-dump-public title=\"Public property\">preventsLazyLoading</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">perPage</span>: <span class=sf-dump-num>15</span>\n              +<span class=sf-dump-public title=\"Public property\">exists</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">wasRecentlyCreated</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">attributes</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ad suscipit labore n</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aute officia rem tem</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet quidem culp</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem adipisci dolo</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quis ducimus numqua</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">original</span>: <span class=sf-dump-note>array:12</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>id</span>\" => <span class=sf-dump-num>12</span>\n                \"<span class=sf-dump-key>creator_id</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>question</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Ad suscipit labore n</span>\"\n                \"<span class=sf-dump-key>option_a</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Aute officia rem tem</span>\"\n                \"<span class=sf-dump-key>option_b</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Eveniet quidem culp</span>\"\n                \"<span class=sf-dump-key>option_c</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Quidem adipisci dolo</span>\"\n                \"<span class=sf-dump-key>option_d</span>\" => \"<span class=sf-dump-str title=\"19 characters\">Quis ducimus numqua</span>\"\n                \"<span class=sf-dump-key>correct_option</span>\" => \"<span class=sf-dump-str>A</span>\"\n                \"<span class=sf-dump-key>is_active</span>\" => <span class=sf-dump-num>1</span>\n                \"<span class=sf-dump-key>created_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>updated_at</span>\" => \"<span class=sf-dump-str title=\"19 characters\">2025-06-13 22:36:43</span>\"\n                \"<span class=sf-dump-key>deleted_at</span>\" => <span class=sf-dump-const>null</span>\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">changes</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">casts</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                \"<span class=sf-dump-key>deleted_at</span>\" => \"<span class=sf-dump-str title=\"8 characters\">datetime</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">classCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">attributeCastCache</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dateFormat</span>: <span class=sf-dump-const>null</span>\n              #<span class=sf-dump-protected title=\"Protected property\">appends</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">dispatchesEvents</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">observables</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">relations</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">touches</span>: []\n              +<span class=sf-dump-public title=\"Public property\">timestamps</span>: <span class=sf-dump-const>true</span>\n              +<span class=sf-dump-public title=\"Public property\">usesUniqueIds</span>: <span class=sf-dump-const>false</span>\n              #<span class=sf-dump-protected title=\"Protected property\">hidden</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">visible</span>: []\n              #<span class=sf-dump-protected title=\"Protected property\">fillable</span>: <span class=sf-dump-note>array:8</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">creator_id</span>\"\n                <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"8 characters\">question</span>\"\n                <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"8 characters\">option_a</span>\"\n                <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"8 characters\">option_b</span>\"\n                <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"8 characters\">option_c</span>\"\n                <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"8 characters\">option_d</span>\"\n                <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"14 characters\">correct_option</span>\"\n                <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"9 characters\">is_active</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">guarded</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>*</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">cascadeDeletes</span>: <span class=sf-dump-note>array:1</span> [<samp data-depth=8 class=sf-dump-compact>\n                <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">answers</span>\"\n              </samp>]\n              #<span class=sf-dump-protected title=\"Protected property\">forceDeleting</span>: <span class=sf-dump-const>false</span>\n            </samp>}\n          </samp>]\n          #<span class=sf-dump-protected title=\"Protected property\">escapeWhenCastingToString</span>: <span class=sf-dump-const>false</span>\n        </samp>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>5</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>22</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\View\\Engines\\PhpEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/09eeb4d462694766cabe342addee4cae.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"5 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"5 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"5 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"5 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>6</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"71 characters\">vendor/laravel/framework/src/Illuminate/View/Engines/CompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>72</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"12 characters\">evaluatePath</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"108 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\storage\\framework\\views/09eeb4d462694766cabe342addee4cae.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"5 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"5 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"5 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"5 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>7</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"78 characters\">vendor/livewire/livewire/src/Mechanisms/ExtendBlade/ExtendedCompilerEngine.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>10</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"38 characters\">Illuminate\\View\\Engines\\CompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"90 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/test.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"5 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"5 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"5 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"5 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>8</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>207</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"3 characters\">get</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Livewire\\Mechanisms\\ExtendBlade\\ExtendedCompilerEngine</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"90 characters\">E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/test.blade.php</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>__env</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\View\\Factory\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\View</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Factory</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2928 title=\"5 occurrences\">#928</a> &#8230;23}\n        \"<span class=sf-dump-key>app</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Foundation\\Application\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Foundation</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Application</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref22 title=\"5 occurrences\">#2</a> &#8230;40}\n        \"<span class=sf-dump-key>errors</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Support\\ViewErrorBag\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Support</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">ViewErrorBag</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref2927 title=\"5 occurrences\">#927</a>}\n        \"<span class=sf-dump-key>test</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"App\\Models\\Quiz\\QuizTest\\QuizTest\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">App\\Models\\Quiz\\QuizTest</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">QuizTest</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21943 title=\"5 occurrences\">#1943</a>}\n        \"<span class=sf-dump-key>questions</span>\" => <span class=\"sf-dump-note sf-dump-ellipsization\" title=\"Illuminate\\Database\\Eloquent\\Collection\n\"><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">Illuminate\\Database\\Eloquent</span><span class=\"sf-dump-ellipsis sf-dump-ellipsis-note\">\\</span><span class=\"sf-dump-ellipsis-tail\">Collection</span></span> {<a class=sf-dump-ref href=#sf-dump-**********-ref21973 title=\"5 occurrences\">#1973</a>}\n        \"<span class=sf-dump-key>existingAnswers</span>\" => []\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>9</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>190</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">getContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>10</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"53 characters\">vendor/laravel/framework/src/Illuminate/View/View.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>159</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"14 characters\">renderContents</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>11</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">render</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"20 characters\">Illuminate\\View\\View</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => []\n  </samp>]\n  <span class=sf-dump-index>12</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"57 characters\">vendor/laravel/framework/src/Illuminate/Http/Response.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>35</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">setContent</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>13</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>918</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"11 characters\">__construct</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"24 characters\">Illuminate\\Http\\Response</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n      <span class=sf-dump-index>1</span> => <span class=sf-dump-num>200</span>\n      <span class=sf-dump-index>2</span> => <span class=sf-dump-note>array:1</span> [<samp data-depth=4 class=sf-dump-compact>\n        \"<span class=sf-dump-key>Content-Type</span>\" => \"<span class=sf-dump-str title=\"9 characters\">text/html</span>\"\n      </samp>]\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>14</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>885</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"10 characters\">toResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">::</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>15</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">prepareResponse</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"29 characters\">[object Illuminate\\View\\View]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>16</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Routing\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>17</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>18</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>19</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"50 characters\">vendor/realrashid/sweet-alert/src/ToSweetAlert.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>69</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>20</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"34 characters\">RealRashid\\SweetAlert\\ToSweetAlert</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>21</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"81 characters\">vendor/laravel/framework/src/Illuminate/Routing/Middleware/SubstituteBindings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>50</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>22</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"48 characters\">Illuminate\\Routing\\Middleware\\SubstituteBindings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>23</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"86 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/VerifyCsrfToken.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>78</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>24</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"53 characters\">Illuminate\\Foundation\\Http\\Middleware\\VerifyCsrfToken</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>25</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/View/Middleware/ShareErrorsFromSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>26</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\View\\Middleware\\ShareErrorsFromSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>27</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>121</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>28</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"75 characters\">vendor/laravel/framework/src/Illuminate/Session/Middleware/StartSession.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>64</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"21 characters\">handleStatefulRequest</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:3</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Session\\Store]</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>29</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"42 characters\">Illuminate\\Session\\Middleware\\StartSession</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>30</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/AddQueuedCookiesToResponse.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>37</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>31</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Cookie\\Middleware\\AddQueuedCookiesToResponse</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>32</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"76 characters\">vendor/laravel/framework/src/Illuminate/Cookie/Middleware/EncryptCookies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>67</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>33</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Illuminate\\Cookie\\Middleware\\EncryptCookies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>34</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>35</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>805</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>36</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>784</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"19 characters\">runRouteWithinStack</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>37</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>748</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">runRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"33 characters\">[object Illuminate\\Routing\\Route]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>38</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"58 characters\">vendor/laravel/framework/src/Illuminate/Routing/Router.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>737</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"15 characters\">dispatchToRoute</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>39</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>200</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"8 characters\">dispatch</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"25 characters\">Illuminate\\Routing\\Router</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>40</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"36 characters\">Illuminate\\Foundation\\Http\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>41</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"106 characters\">vendor/livewire/livewire/src/Features/SupportDisablingBackButtonCache/DisableBackButtonCacheMiddleware.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>19</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>42</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"82 characters\">Livewire\\Features\\SupportDisablingBackButtonCache\\DisableBackButtonCacheMiddleware</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>43</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/barryvdh/laravel-debugbar/src/Middleware/InjectDebugbar.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>66</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>44</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"43 characters\">Barryvdh\\Debugbar\\Middleware\\InjectDebugbar</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>45</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>46</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"96 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ConvertEmptyStringsToNull.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>31</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>47</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"63 characters\">Illuminate\\Foundation\\Http\\Middleware\\ConvertEmptyStringsToNull</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>48</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"88 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TransformsRequest.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>21</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>49</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"82 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/TrimStrings.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>40</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"55 characters\">Illuminate\\Foundation\\Http\\Middleware\\TransformsRequest</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>50</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"49 characters\">Illuminate\\Foundation\\Http\\Middleware\\TrimStrings</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>51</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"87 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/ValidatePostSize.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>27</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>52</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"54 characters\">Illuminate\\Foundation\\Http\\Middleware\\ValidatePostSize</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>53</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"103 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Middleware/PreventRequestsDuringMaintenance.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>99</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>54</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"70 characters\">Illuminate\\Foundation\\Http\\Middleware\\PreventRequestsDuringMaintenance</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>55</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"70 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/HandleCors.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>49</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>56</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"37 characters\">Illuminate\\Http\\Middleware\\HandleCors</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>57</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"72 characters\">vendor/laravel/framework/src/Illuminate/Http/Middleware/TrustProxies.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>39</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>58</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>183</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"39 characters\">Illuminate\\Http\\Middleware\\TrustProxies</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>59</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"61 characters\">vendor/laravel/framework/src/Illuminate/Pipeline/Pipeline.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>119</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"29 characters\">Illuminate\\Pipeline\\{closure}</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>60</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>175</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"4 characters\">then</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"28 characters\">Illuminate\\Pipeline\\Pipeline</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">[object Closure]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>61</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"66 characters\">vendor/laravel/framework/src/Illuminate/Foundation/Http/Kernel.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>144</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"24 characters\">sendRequestThroughRouter</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n  <span class=sf-dump-index>62</span> => <span class=sf-dump-note>array:6</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>file</span>\" => \"<span class=sf-dump-str title=\"16 characters\">public/index.php</span>\"\n    \"<span class=sf-dump-key>line</span>\" => <span class=sf-dump-num>51</span>\n    \"<span class=sf-dump-key>function</span>\" => \"<span class=sf-dump-str title=\"6 characters\">handle</span>\"\n    \"<span class=sf-dump-key>class</span>\" => \"<span class=sf-dump-str title=\"33 characters\">Illuminate\\Foundation\\Http\\Kernel</span>\"\n    \"<span class=sf-dump-key>type</span>\" => \"<span class=sf-dump-str title=\"2 characters\">-&gt;</span>\"\n    \"<span class=sf-dump-key>args</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"32 characters\">[object Illuminate\\Http\\Request]</span>\"\n    </samp>]\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "surrounding_lines": ["            return $url;\n", "        }\n", "\n", "        throw new RouteNotFoundException(\"Route [{$name}] not defined.\");\n", "    }\n", "\n", "    /**\n"], "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fvendor%2Flaravel%2Fframework%2Fsrc%2FIlluminate%2FRouting%2FUrlGenerator.php&line=477", "ajax": false, "filename": "UrlGenerator.php", "line": "477"}}]}, "views": {"count": 18, "nb_templates": 18, "templates": [{"name": "9x public.quiz.test", "param_count": null, "params": [], "start": **********.256373, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/test.blade.phppublic.quiz.test", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fpublic%2Fquiz%2Ftest.blade.php&line=1", "ajax": false, "filename": "test.blade.php", "line": "?"}, "render_count": 9, "name_original": "public.quiz.test"}, {"name": "9x sweetalert::alert", "param_count": null, "params": [], "start": **********.270935, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 9, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET application/quiz/test/{testId}", "middleware": "web", "controller": "App\\Http\\Controllers\\PublicQuizController@showTest<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=118\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.test.show", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=118\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:118-147</a>"}, "queries": {"count": 3, "nb_statements": 3, "nb_visible_statements": 3, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.01115, "accumulated_duration_str": "11.15ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `id` = 10 and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 385}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 121}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.199585, "duration": 0.00714, "duration_str": "7.14ms", "memory": 0, "memory_str": null, "filename": "HasCustomRouteId.php:19", "source": {"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FTraits%2FHasCustomRouteId.php&line=19", "ajax": false, "filename": "HasCustomRouteId.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 64.036}, {"sql": "select * from `quiz_questions` where `id` in (9, 7, 8, 6, 12, 10, 5, 3, 11, 4) and `quiz_questions`.`deleted_at` is null", "type": "query", "params": [], "bindings": [9, 7, 8, 6, 12, 10, 5, 3, 11, 4], "hints": null, "show_copy": false, "backtrace": [{"index": 15, "namespace": null, "name": "app/Models/Quiz/QuizTest/Relations/QuizTestRelations.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Quiz\\QuizTest\\Relations\\QuizTestRelations.php", "line": 47}, {"index": 16, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 141}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}], "start": **********.226242, "duration": 0.00228, "duration_str": "2.28ms", "memory": 0, "memory_str": null, "filename": "QuizTestRelations.php:47", "source": {"index": 15, "namespace": null, "name": "app/Models/Quiz/QuizTest/Relations/QuizTestRelations.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Models\\Quiz\\QuizTest\\Relations\\QuizTestRelations.php", "line": 47}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizTest%2FRelations%2FQuizTestRelations.php&line=47", "ajax": false, "filename": "QuizTestRelations.php", "line": "47"}, "connection": "blueorange", "explain": null, "start_percent": 64.036, "width_percent": 20.448}, {"sql": "select `selected_option`, `quiz_question_id` from `quiz_answers` where `quiz_answers`.`quiz_test_id` = 10 and `quiz_answers`.`quiz_test_id` is not null and `quiz_answers`.`deleted_at` is null", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 144}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.23954, "duration": 0.00173, "duration_str": "1.73ms", "memory": 0, "memory_str": null, "filename": "PublicQuizController.php:144", "source": {"index": 17, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 144}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=144", "ajax": false, "filename": "PublicQuizController.php", "line": "144"}, "connection": "blueorange", "explain": null, "start_percent": 84.484, "width_percent": 15.516}]}, "models": {"data": {"App\\Models\\Quiz\\QuizQuestion\\QuizQuestion": {"value": 10, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizQuestion%2FQuizQuestion.php&line=1", "ajax": false, "filename": "QuizQuestion.php", "line": "?"}}, "App\\Models\\Quiz\\QuizTest\\QuizTest": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizTest%2FQuizTest.php&line=1", "ajax": false, "filename": "QuizTest.php", "line": "?"}}}, "count": 11, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM", "PHPDEBUGBAR_STACK_DATA": "array:1 [\n  \"01JXWF4GTPZ4REXRXFQPDZQ400\" => null\n]", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test\"\n]", "_flash": "array:2 [\n  \"old\" => array:1 [\n    0 => \"success\"\n  ]\n  \"new\" => array:24 [\n    0 => \"alert.config.title\"\n    1 => \"alert.config.text\"\n    2 => \"alert.config.timer\"\n    3 => \"alert.config.width\"\n    4 => \"alert.config.heightAuto\"\n    5 => \"alert.config.padding\"\n    6 => \"alert.config.showConfirmButton\"\n    7 => \"alert.config.showCloseButton\"\n    8 => \"alert.config.timerProgressBar\"\n    9 => \"alert.config.customClass\"\n    10 => \"alert.config.icon\"\n    11 => \"alert.config\"\n    12 => \"alert.config.title\"\n    13 => \"alert.config.text\"\n    14 => \"alert.config.timer\"\n    15 => \"alert.config.width\"\n    16 => \"alert.config.heightAuto\"\n    17 => \"alert.config.padding\"\n    18 => \"alert.config.showConfirmButton\"\n    19 => \"alert.config.showCloseButton\"\n    20 => \"alert.config.timerProgressBar\"\n    21 => \"alert.config.customClass\"\n    22 => \"alert.config.icon\"\n    23 => \"alert.config\"\n  ]\n]", "success": "Quiz test created successfully! You have 10 minutes to complete 10 questions.", "alert": "[]"}, "request": {"data": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\PublicQuizController@showTest", "uri": "GET application/quiz/test/{testId}", "controller": "App\\Http\\Controllers\\PublicQuizController@showTest<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=118\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=118\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:118-147</a>", "middleware": "web, web", "duration": "1.9s", "peak_memory": "32MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-1497823901 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1497823901\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-206097914 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-206097914\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-1478575868 data-indent-pad=\"  \"><span class=sf-dump-note>array:18</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"45 characters\">https://blueorange.test/application/quiz/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1019 characters\">quiz_test_id=eyJpdiI6Im9zVmJzdStySDlOY2FaZ1ZEaUYxMlE9PSIsInZhbHVlIjoicXh2RkgxRURsalZ4eE9pQVQySnNST0cyQzdWME1uLytzQ0NzSVB0YmhNRXo4WjlFVVFBSmRRc29QUFVReDRWVlJGS0ZKYmNpVFBwbXoxUGJ6NVcydlE9PSIsIm1hYyI6ImUxZDY1ZmEwZjlhYjVmZDljZjNhZTNmZmRjNGM2MDk2MDM0ZGRlMDY4Yzc3ZWEwNTMzOGZmYWU2M2M4YmEwYzIiLCJ0YWciOiIifQ%3D%3D; XSRF-TOKEN=eyJpdiI6IllWNG9PSVl0aVNWM3B5bU83RDQ1TVE9PSIsInZhbHVlIjoicEhDczJYSWlBWmRQTGVyQWtWaTE5TWcvR2poUXVGK1dvR0lmK0tlOTh2RmhQVHVxdXlCS2ZVMy9mcVBOcHZWdmlLWjZUSWpEZmZBT0RXWWlJcExGc0pGN3QyOTdVVi80eWZ0eDBJbnZxay90K2NEOHlmSXB0bFZURFB5eGZIUm8iLCJtYWMiOiI2ODA1OGJkOTU4NWNlYTZhMDdjY2JjYzVhMzBkOTU1OWJkODE3NmVkODBjZWE1MjYyYzZmZjUwZDk1MGZlZmM0IiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlB6ZnZuNGc4bC9mL3lCUXBFOGlyS1E9PSIsInZhbHVlIjoiQmlwc21TeHdDUWtIQTNBVnVvYVY4cGZreStLY3lRN2k5VTBsRzd1NGVzRVRFYXFQVHpYZ0xQNEdvQkFxZWpQbVhNZm5jTHlUNWdOdWRNWWZMOE1CYVI2aUdXOUdKRkNmYTEyRnllVHlTdGRLTGY0OVgxYllNYlJUd2luRitPMDUiLCJtYWMiOiIxOGFiMDA4NjVjZTc1OTZjZDgyOWY0YzBlOTBkYWM4OTczMDM1ZTliZGFmYWIwNjkxNjI3OTgyMjA0MTA3NjA0IiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1478575868\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-525126118 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>quiz_test_id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AFMC64tXglhKprsQQSyIQPheMvMySaThtMI01Ous</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-525126118\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-582476754 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 13:33:54 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-582476754\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-424545350 data-indent-pad=\"  \"><span class=sf-dump-note>array:6</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>01JXWF4GTPZ4REXRXFQPDZQ400</span>\" => <span class=sf-dump-const>null</span>\n  </samp>]\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://blueorange.test/application/quiz/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"7 characters\">success</span>\"\n    </samp>]\n    \"<span class=sf-dump-key>new</span>\" => <span class=sf-dump-note>array:24</span> [<samp data-depth=3 class=sf-dump-compact>\n      <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>1</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>2</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>3</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>4</span> => \"<span class=sf-dump-str title=\"23 characters\">alert.config.heightAuto</span>\"\n      <span class=sf-dump-index>5</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>6</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>7</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>8</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>9</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>10</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>11</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n      <span class=sf-dump-index>12</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.title</span>\"\n      <span class=sf-dump-index>13</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.text</span>\"\n      <span class=sf-dump-index>14</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.timer</span>\"\n      <span class=sf-dump-index>15</span> => \"<span class=sf-dump-str title=\"18 characters\">alert.config.width</span>\"\n      <span class=sf-dump-index>16</span> => \"<span class=sf-dump-str title=\"23 characters\">alert.config.heightAuto</span>\"\n      <span class=sf-dump-index>17</span> => \"<span class=sf-dump-str title=\"20 characters\">alert.config.padding</span>\"\n      <span class=sf-dump-index>18</span> => \"<span class=sf-dump-str title=\"30 characters\">alert.config.showConfirmButton</span>\"\n      <span class=sf-dump-index>19</span> => \"<span class=sf-dump-str title=\"28 characters\">alert.config.showCloseButton</span>\"\n      <span class=sf-dump-index>20</span> => \"<span class=sf-dump-str title=\"29 characters\">alert.config.timerProgressBar</span>\"\n      <span class=sf-dump-index>21</span> => \"<span class=sf-dump-str title=\"24 characters\">alert.config.customClass</span>\"\n      <span class=sf-dump-index>22</span> => \"<span class=sf-dump-str title=\"17 characters\">alert.config.icon</span>\"\n      <span class=sf-dump-index>23</span> => \"<span class=sf-dump-str title=\"12 characters\">alert.config</span>\"\n    </samp>]\n  </samp>]\n  \"<span class=sf-dump-key>success</span>\" => \"<span class=sf-dump-str title=\"77 characters\">Quiz test created successfully! You have 10 minutes to complete 10 questions.</span>\"\n  \"<span class=sf-dump-key>alert</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-424545350\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "500 Internal Server Error", "full_url": "https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA", "action_name": "application.quiz.test.show", "controller_action": "App\\Http\\Controllers\\PublicQuizController@showTest"}, "badge": "500 Internal Server Error"}}