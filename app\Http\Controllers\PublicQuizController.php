<?php

namespace App\Http\Controllers;

use Exception;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use App\Models\Quiz\QuizTest\QuizTest;
use App\Models\Quiz\QuizAnswer\QuizAnswer;
use App\Models\Quiz\QuizQuestion\QuizQuestion;

class PublicQuizController extends Controller
{
    /**
     * Show candidate registration form for walk-in quiz
     */
    public function index()
    {
        return view('public.quiz.register');
    }

    /**
     * Create a new test for walk-in candidate
     */
    public function createWalkInTest(Request $request)
    {
        $request->validate([
            'candidate_name' => 'required|string|max:255',
            'candidate_email' => 'required|email|max:255',
        ]);

        try {
            DB::transaction(function () use ($request, &$test) {
                // Get random questions for walk-in test
                $availableQuestions = QuizQuestion::where('is_active', true)->pluck('id')->toArray();
                
                if (count($availableQuestions) < 10) {
                    throw new Exception('Not enough active questions available for the quiz.');
                }
                
                $questionIds = array_slice(
                    array_rand(array_flip($availableQuestions), 10),
                    0,
                    10
                );

                $test = QuizTest::create([
                    'creator_id' => null, // Walk-in test has no creator
                    'candidate_name' => $request->candidate_name,
                    'candidate_email' => $request->candidate_email,
                    'total_questions' => 10,
                    'total_time' => 10, // 10 minutes
                    'passing_score' => 6,
                    'question_ids' => $questionIds,
                    'status' => 'Pending',
                ]);
            });

            // Store test ID in session for tracking
            session(['quiz_test_id' => $test->id]);
            
            return redirect()->route('quiz.test.show', $test->id);
        } catch (Exception $e) {
            return redirect()->back()
                ->withInput()
                ->withErrors(['error' => $e->getMessage()]);
        }
    }

    /**
     * Show quiz test for candidate
     */
    public function showTest($testId)
    {
        $test = QuizTest::findOrFail($testId);
        
        // Check if test is accessible
        if ($test->status === 'Cancelled') {
            abort(404, 'This quiz test is no longer available.');
        }
        
        if ($test->status === 'Completed') {
            return $this->showResults($test);
        }

        // Store test ID in session
        session(['quiz_test_id' => $test->id]);
        
        // Get questions for the test
        $questions = $test->questions();
        
        // Get existing answers if any
        $existingAnswers = $test->answers()->pluck('selected_option', 'quiz_question_id')->toArray();
        
        return view('public.quiz.test', compact(['test', 'questions', 'existingAnswers']));
    }

    /**
     * Start the quiz test
     */
    public function startTest(Request $request, $testId)
    {
        $test = QuizTest::findOrFail($testId);
        
        if ($test->status !== 'Pending') {
            return response()->json(['error' => 'Test cannot be started.'], 400);
        }

        try {
            $test->update([
                'status' => 'Running',
                'started_at' => Carbon::now(),
            ]);

            return response()->json([
                'success' => true,
                'started_at' => $test->started_at->toISOString(),
                'total_time' => $test->total_time,
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Submit an answer for a question
     */
    public function submitAnswer(Request $request, $testId)
    {
        $request->validate([
            'question_id' => 'required|exists:quiz_questions,id',
            'selected_option' => 'required|in:A,B,C,D',
        ]);

        $test = QuizTest::findOrFail($testId);
        
        if ($test->status !== 'Running') {
            return response()->json(['error' => 'Test is not running.'], 400);
        }

        // Check if question belongs to this test
        if (!in_array($request->question_id, $test->question_ids)) {
            return response()->json(['error' => 'Invalid question for this test.'], 400);
        }

        try {
            $question = QuizQuestion::findOrFail($request->question_id);
            $isCorrect = $question->correct_option === $request->selected_option;

            // Update or create answer
            QuizAnswer::updateOrCreate(
                [
                    'quiz_test_id' => $test->id,
                    'quiz_question_id' => $request->question_id,
                ],
                [
                    'selected_option' => $request->selected_option,
                    'is_correct' => $isCorrect,
                    'answered_at' => Carbon::now(),
                ]
            );

            return response()->json(['success' => true]);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Submit the entire quiz
     */
    public function submitQuiz(Request $request, $testId)
    {
        $test = QuizTest::findOrFail($testId);
        
        if ($test->status !== 'Running') {
            return response()->json(['error' => 'Test is not running.'], 400);
        }

        try {
            DB::transaction(function () use ($test) {
                $totalScore = $test->answers()->where('is_correct', true)->count();
                $attemptedQuestions = $test->answers()->count();

                $test->update([
                    'status' => 'Completed',
                    'ended_at' => Carbon::now(),
                    'attempted_questions' => $attemptedQuestions,
                    'total_score' => $totalScore,
                    'auto_submitted' => false,
                ]);
            });

            return response()->json([
                'success' => true,
                'redirect_url' => route('quiz.test.results', $test->id)
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Auto-submit quiz when time expires
     */
    public function autoSubmitQuiz($testId)
    {
        $test = QuizTest::findOrFail($testId);
        
        if ($test->status !== 'Running') {
            return response()->json(['error' => 'Test is not running.'], 400);
        }

        try {
            DB::transaction(function () use ($test) {
                $totalScore = $test->answers()->where('is_correct', true)->count();
                $attemptedQuestions = $test->answers()->count();

                $test->update([
                    'status' => 'Completed',
                    'ended_at' => Carbon::now(),
                    'attempted_questions' => $attemptedQuestions,
                    'total_score' => $totalScore,
                    'auto_submitted' => true,
                ]);
            });

            return response()->json([
                'success' => true,
                'redirect_url' => route('quiz.test.results', $test->id)
            ]);
        } catch (Exception $e) {
            return response()->json(['error' => $e->getMessage()], 500);
        }
    }

    /**
     * Show quiz results
     */
    public function showResults($testId)
    {
        if ($testId instanceof QuizTest) {
            $test = $testId;
        } else {
            $test = QuizTest::findOrFail($testId);
        }
        
        if ($test->status !== 'Completed') {
            abort(404, 'Results not available yet.');
        }

        $test->load(['answers.question']);
        $questions = $test->questions();
        
        $passed = $test->total_score >= $test->passing_score;
        $percentage = round(($test->total_score / $test->total_questions) * 100, 2);
        
        return view('public.quiz.results', compact(['test', 'questions', 'passed', 'percentage']));
    }

    /**
     * Check test status (for AJAX polling)
     */
    public function checkTestStatus($testId)
    {
        $test = QuizTest::findOrFail($testId);
        
        $timeRemaining = null;
        if ($test->status === 'Running' && $test->started_at) {
            $endTime = $test->started_at->addMinutes($test->total_time);
            $timeRemaining = max(0, Carbon::now()->diffInSeconds($endTime, false));
            
            // Auto-submit if time expired
            if ($timeRemaining <= 0) {
                $this->autoSubmitQuiz($testId);
                $test->refresh();
            }
        }

        return response()->json([
            'status' => $test->status,
            'time_remaining' => $timeRemaining,
            'started_at' => $test->started_at?->toISOString(),
            'ended_at' => $test->ended_at?->toISOString(),
        ]);
    }
}
