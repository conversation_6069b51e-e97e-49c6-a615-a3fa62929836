{"__meta": {"id": "01JXWG2E888GTR0QQDE4KEHTC8", "datetime": "2025-06-16 19:50:12", "utime": **********.745529, "method": "GET", "uri": "/application/quiz/test", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750081811.952649, "end": **********.745558, "duration": 0.7929089069366455, "duration_str": "793ms", "measures": [{"label": "Booting", "start": 1750081811.952649, "relative_start": 0, "end": **********.611353, "relative_end": **********.611353, "duration": 0.****************, "duration_str": "659ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.611377, "relative_start": 0.****************, "end": **********.74556, "relative_end": 1.9073486328125e-06, "duration": 0.*****************, "duration_str": "134ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.640486, "relative_start": 0.****************, "end": **********.645128, "relative_end": **********.645128, "duration": 0.004642009735107422, "duration_str": "4.64ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.724022, "relative_start": 0.****************, "end": **********.741779, "relative_end": **********.741779, "duration": 0.017757177352905273, "duration_str": "17.76ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "26MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 3, "nb_templates": 3, "templates": [{"name": "1x public.quiz.register", "param_count": null, "params": [], "start": **********.728713, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/public/quiz/register.blade.phppublic.quiz.register", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fpublic%2Fquiz%2Fregister.blade.php&line=1", "ajax": false, "filename": "register.blade.php", "line": "?"}, "render_count": 1, "name_original": "public.quiz.register"}, {"name": "1x layouts.public.app", "param_count": null, "params": [], "start": **********.739448, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/layouts/public/app.blade.phplayouts.public.app", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Flayouts%2Fpublic%2Fapp.blade.php&line=1", "ajax": false, "filename": "app.blade.php", "line": "?"}, "render_count": 1, "name_original": "layouts.public.app"}, {"name": "1x sweetalert::alert", "param_count": null, "params": [], "start": **********.74076, "type": "blade", "hash": "bladeE:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\resources\\views/vendor/sweetalert/alert.blade.phpsweetalert::alert", "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fresources%2Fviews%2Fvendor%2Fsweetalert%2Falert.blade.php&line=1", "ajax": false, "filename": "alert.blade.php", "line": "?"}, "render_count": 1, "name_original": "sweetalert::alert"}]}, "route": {"uri": "GET application/quiz/test", "middleware": "web", "controller": "App\\Http\\Controllers\\PublicQuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.register", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:19-40</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00598, "accumulated_duration_str": "5.98ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `testid` = 'SIQT20250616ZNF4' and `status` != 'Completed' and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": ["SIQT20250616ZNF4", "Completed"], "hints": null, "show_copy": false, "backtrace": [{"index": 16, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 31}, {"index": 17, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 18, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}, {"index": 19, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 259}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Route.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Route.php", "line": 205}], "start": **********.712126, "duration": 0.00598, "duration_str": "5.98ms", "memory": 0, "memory_str": null, "filename": "PublicQuizController.php:31", "source": {"index": 16, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 31}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=31", "ajax": false, "filename": "PublicQuizController.php", "line": "31"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": [], "count": 0, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.register", "controller_action": "App\\Http\\Controllers\\PublicQuizController@index", "uri": "GET application/quiz/test", "controller": "App\\Http\\Controllers\\PublicQuizController@index<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=19\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:19-40</a>", "middleware": "web, web", "duration": "791ms", "peak_memory": "28MB", "response": "text/html; charset=UTF-8", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-973302607 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-973302607\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-1144321240 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-1144321240\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-119764935 data-indent-pad=\"  \"><span class=sf-dump-note>array:17</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">max-age=0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>upgrade-insecure-requests</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"135 characters\">text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">none</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">navigate</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-user</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"8 characters\">document</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1379 characters\">quiz_test_id=eyJpdiI6Im9zVmJzdStySDlOY2FaZ1ZEaUYxMlE9PSIsInZhbHVlIjoicXh2RkgxRURsalZ4eE9pQVQySnNST0cyQzdWME1uLytzQ0NzSVB0YmhNRXo4WjlFVVFBSmRRc29QUFVReDRWVlJGS0ZKYmNpVFBwbXoxUGJ6NVcydlE9PSIsIm1hYyI6ImUxZDY1ZmEwZjlhYjVmZDljZjNhZTNmZmRjNGM2MDk2MDM0ZGRlMDY4Yzc3ZWEwNTMzOGZmYWU2M2M4YmEwYzIiLCJ0YWciOiIifQ%3D%3D; laravel_session=eyJpdiI6Ik5QSUhCRE1Ka2JwaDByYTVVcmRZVkE9PSIsInZhbHVlIjoiZ0I0Y05sbzRzbW8yeFNhZ0NFWjJ3aXYzOUkwTzlDcmFvUVBZbTJSaDJUODVFUndkM0NnMHpWd3d4d3VJS2QwMjNmRVZpaUYrbUsrcXVPRkxrMGttcHF1TmYxc1JHdzc1K21MeE5PK1RUWmJnTzNEM1dSdklkc0lsQWFudWZWbzYiLCJtYWMiOiI0OTdmMTM1MWM3YzI2ZDI1ODVkNWFlNzEyZGNkZWUxZTE1N2IyYTU4NzVjZjYzMTVkMjRjYTcyMDcxNWMxOTI3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6Ilhxcm84OFF1SnplV21mM2NDWm9PbHc9PSIsInZhbHVlIjoiTjA2NGVqRmp3VmhGWmtXZEIyL2pkSmh6cDNQUm82TDBudTZnRVZESXViOTU5SDA1aFR6WVpSTkpjRzZoQ1ZyaHZXT2VSdnoxUVFvUjJZa1BOTnM3K2ZqTnNmYjVkMEtFL2tGYU9CWWxVcUtQNUttM3R5R3VIeW9kRFZqWkVLY2ciLCJtYWMiOiJmZmZkOTVhZWQ0ZDk5MjZhY2U4MmMwOGFkN2RkNmEzMzAzNGZmNjM1MjEwZGRjMTlmYmM4OGMwODgyODVkZTAzIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IkJmTVpCL1hyTzd3czBEU0JMemhkYkE9PSIsInZhbHVlIjoiRUsycXdDZDYyVHRTVkxpQSs0TGtGVjkxNTVoa0ZPdXhBZjNnNlVyWGJSU3I4YnJCTis3aVZsNWZjRjN0NnRlZC96TEZxajVwdGRLMUhwQ2NwZllFbVR0aExMVEpKdkJVOTd4U0hPWCtOMkNweXNHSjczR3ZORVNoaUFmQW5COUIiLCJtYWMiOiI3NzNmZmI2NTJkNmI4NzUyNmJkYzdhNTg5YmJmOWE4Yjk2MjUxMTE4YWI1NDViZTRkMDA2NGNlYjk3NmNmMmUzIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-119764935\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-********** data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>quiz_test_id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zOgYDYsZpjT8OLuisTWWHn0ROfzVy1aTCyMaZh59</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AFMC64tXglhKprsQQSyIQPheMvMySaThtMI01Ous</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-**********\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-179130236 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"24 characters\">text/html; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 13:50:12 GMT</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-179130236\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-12938038 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"45 characters\">https://blueorange.test/application/quiz/test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-12938038\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "200 OK", "full_url": "https://blueorange.test/application/quiz/test", "action_name": "application.quiz.register", "controller_action": "App\\Http\\Controllers\\PublicQuizController@index"}, "badge": null}}