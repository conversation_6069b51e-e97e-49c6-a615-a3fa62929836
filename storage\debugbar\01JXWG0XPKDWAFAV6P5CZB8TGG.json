{"__meta": {"id": "01JXWG0XPKDWAFAV6P5CZB8TGG", "datetime": "2025-06-16 19:49:23", "utime": **********.029645, "method": "POST", "uri": "/application/quiz/test/J9a2D4Q0k56RodvA/submit", "ip": "127.0.0.1"}, "php": {"version": "8.3.20", "interface": "apache2handler"}, "messages": {"count": 0, "messages": []}, "time": {"start": 1750081761.360216, "end": **********.029683, "duration": 1.6694672107696533, "duration_str": "1.67s", "measures": [{"label": "Booting", "start": 1750081761.360216, "relative_start": 0, "end": **********.715362, "relative_end": **********.715362, "duration": 1.****************, "duration_str": "1.36s", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Application", "start": **********.715588, "relative_start": 1.****************, "end": **********.029686, "relative_end": 2.86102294921875e-06, "duration": 0.*****************, "duration_str": "314ms", "memory": 0, "memory_str": "0B", "params": [], "collector": "time", "group": null}, {"label": "Routing", "start": **********.855995, "relative_start": 1.***************, "end": **********.903555, "relative_end": **********.903555, "duration": 0.*****************, "duration_str": "47.56ms", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}, {"label": "Preparing Response", "start": **********.023016, "relative_start": 1.****************, "end": **********.023948, "relative_end": **********.023948, "duration": 0.0009319782257080078, "duration_str": "932μs", "memory": 0, "memory_str": "0B", "params": [], "collector": null, "group": null}]}, "memory": {"peak_usage": ********, "peak_usage_str": "25MB"}, "exceptions": {"count": 0, "exceptions": []}, "views": {"count": 0, "nb_templates": 0, "templates": []}, "route": {"uri": "POST application/quiz/test/{testId}/submit", "middleware": "web", "controller": "App\\Http\\Controllers\\PublicQuizController@submitQuiz<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=239\" class=\"phpdebugbar-widgets-editor-link\"></a>", "as": "application.quiz.test.submit", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=239\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:239-276</a>"}, "queries": {"count": 1, "nb_statements": 1, "nb_visible_statements": 1, "nb_excluded_statements": 0, "nb_failed_statements": 0, "accumulated_duration": 0.00634, "accumulated_duration_str": "6.34ms", "memory_usage": 0, "memory_usage_str": null, "statements": [{"sql": "select * from `quiz_tests` where `id` = 10 and `quiz_tests`.`deleted_at` is null limit 1", "type": "query", "params": [], "bindings": [10], "hints": null, "show_copy": false, "backtrace": [{"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, {"index": 18, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 385}, {"index": 19, "namespace": null, "name": "app/Http/Controllers/PublicQuizController.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Http\\Controllers\\PublicQuizController.php", "line": 241}, {"index": 20, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/Controller.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\Controller.php", "line": 54}, {"index": 21, "namespace": null, "name": "vendor/laravel/framework/src/Illuminate/Routing/ControllerDispatcher.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\vendor\\laravel\\framework\\src\\Illuminate\\Routing\\ControllerDispatcher.php", "line": 43}], "start": **********.995161, "duration": 0.00634, "duration_str": "6.34ms", "memory": 0, "memory_str": null, "filename": "HasCustomRouteId.php:19", "source": {"index": 17, "namespace": null, "name": "app/Traits/HasCustomRouteId.php", "file": "E:\\NIGEL\\LaravelProjects\\laragon\\www\\BlueOrange\\app\\Traits\\HasCustomRouteId.php", "line": 19}, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FTraits%2FHasCustomRouteId.php&line=19", "ajax": false, "filename": "HasCustomRouteId.php", "line": "19"}, "connection": "blueorange", "explain": null, "start_percent": 0, "width_percent": 100}]}, "models": {"data": {"App\\Models\\Quiz\\QuizTest\\QuizTest": {"value": 1, "xdebug_link": {"url": "phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FModels%2FQuiz%2FQuizTest%2FQuizTest.php&line=1", "ajax": false, "filename": "QuizTest.php", "line": "?"}}}, "count": 1, "is_counter": true}, "livewire": {"data": [], "count": 0}, "symfonymailer_mails": {"count": 0, "mails": []}, "gate": {"count": 0, "messages": []}, "session": {"_token": "a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM", "_previous": "array:1 [\n  \"url\" => \"https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA\"\n]", "_flash": "array:2 [\n  \"old\" => []\n  \"new\" => []\n]", "alert": "[]", "PHPDEBUGBAR_STACK_DATA": "[]"}, "request": {"data": {"status": "400 Bad Request", "full_url": "https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA/submit", "action_name": "application.quiz.test.submit", "controller_action": "App\\Http\\Controllers\\PublicQuizController@submitQuiz", "uri": "POST application/quiz/test/{testId}/submit", "controller": "App\\Http\\Controllers\\PublicQuizController@submitQuiz<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=239\" class=\"phpdebugbar-widgets-editor-link\"></a>", "prefix": "application/quiz", "file": "<a href=\"phpstorm://open?file=E%3A%2FNIGEL%2FLaravelProjects%2Flaragon%2Fwww%2FBlueOrange%2Fapp%2FHttp%2FControllers%2FPublicQuizController.php&line=239\" onclick=\"\" class=\"phpdebugbar-widgets-editor-link\">app/Http/Controllers/PublicQuizController.php:239-276</a>", "middleware": "web, web", "duration": "1.67s", "peak_memory": "26MB", "response": "application/json", "request_format": "html", "request_query": "<pre class=sf-dump id=sf-dump-52288766 data-indent-pad=\"  \">[]\n</pre><script>Sfdump(\"sf-dump-52288766\", {\"maxDepth\":0})</script>\n", "request_request": "<pre class=sf-dump id=sf-dump-55376942 data-indent-pad=\"  \"><span class=sf-dump-note>array:1</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-55376942\", {\"maxDepth\":0})</script>\n", "request_headers": "<pre class=sf-dump id=sf-dump-374360692 data-indent-pad=\"  \"><span class=sf-dump-note>array:19</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>host</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"15 characters\">blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>connection</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"10 characters\">keep-alive</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-length</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">47</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-platform</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"9 characters\">&quot;Windows&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"57 characters\">&quot;Chromium&quot;;v=&quot;134&quot;, &quot;Not:A-Brand&quot;;v=&quot;24&quot;, &quot;Opera&quot;;v=&quot;119&quot;</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-ch-ua-mobile</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"2 characters\">?0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>x-requested-with</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">XMLHttpRequest</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>user-agent</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"125 characters\">Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/134.0.0.0 Safari/537.36 OPR/119.0.0.0</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"3 characters\">*/*</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>dnt</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str>1</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"48 characters\">application/x-www-form-urlencoded; charset=UTF-8</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>origin</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">https://blueorange.test</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-site</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"11 characters\">same-origin</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-mode</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"4 characters\">cors</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>sec-fetch-dest</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"5 characters\">empty</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>referer</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"62 characters\">https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-encoding</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"23 characters\">gzip, deflate, br, zstd</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>accept-language</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"14 characters\">en-US,en;q=0.9</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>cookie</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"1379 characters\">quiz_test_id=eyJpdiI6Im9zVmJzdStySDlOY2FaZ1ZEaUYxMlE9PSIsInZhbHVlIjoicXh2RkgxRURsalZ4eE9pQVQySnNST0cyQzdWME1uLytzQ0NzSVB0YmhNRXo4WjlFVVFBSmRRc29QUFVReDRWVlJGS0ZKYmNpVFBwbXoxUGJ6NVcydlE9PSIsIm1hYyI6ImUxZDY1ZmEwZjlhYjVmZDljZjNhZTNmZmRjNGM2MDk2MDM0ZGRlMDY4Yzc3ZWEwNTMzOGZmYWU2M2M4YmEwYzIiLCJ0YWciOiIifQ%3D%3D; laravel_session=eyJpdiI6Ik5QSUhCRE1Ka2JwaDByYTVVcmRZVkE9PSIsInZhbHVlIjoiZ0I0Y05sbzRzbW8yeFNhZ0NFWjJ3aXYzOUkwTzlDcmFvUVBZbTJSaDJUODVFUndkM0NnMHpWd3d4d3VJS2QwMjNmRVZpaUYrbUsrcXVPRkxrMGttcHF1TmYxc1JHdzc1K21MeE5PK1RUWmJnTzNEM1dSdklkc0lsQWFudWZWbzYiLCJtYWMiOiI0OTdmMTM1MWM3YzI2ZDI1ODVkNWFlNzEyZGNkZWUxZTE1N2IyYTU4NzVjZjYzMTVkMjRjYTcyMDcxNWMxOTI3IiwidGFnIjoiIn0%3D; XSRF-TOKEN=eyJpdiI6IjN0SE5lV3BYdEFJODJoVnlNQkcvcmc9PSIsInZhbHVlIjoiTlNwSjByb0hJak0xRkE2eERVcHNOaW4vVmNDdm5OcEtPWUZzNUp4YUVzaFZCa2thc0tmbG92WU16MnByaHl6ZWxRWS8wVk9GK0NhNFlPV0RLeHRGMHF4NWVxZHNlV0k4Uy9JOU51S3BmNkZlWlZIZ2IxLysxclh3UENacmVHRzciLCJtYWMiOiJjN2QyODhlYjcyNWE5MjQzZjg5MDUxNzUxOTIzMTJhNTk3ODM1NjE0ZjA5MDNkNTkyMzRmODYwODBkYzViNGMyIiwidGFnIjoiIn0%3D; si_app_session=eyJpdiI6IlRqUCtkckRUQStSeUJUS2RleFlBenc9PSIsInZhbHVlIjoiemd4QkZYdTRKaVF2NlQ4OUdpNXpHREIrazRZaDNFN295d2xNVmxUanRxelRrZkk3VXlkcm1XZjFsSlV5S0dZS2x2WmFqd1VWS1YzaDZlM0tWTGdVUlZ0Nm9NSVl3ZEVNZGpGak9wblQydUtHejlPTDVEdC9CUGE0bjlGbzBHc0kiLCJtYWMiOiIzMTMyMDdjODcxZWE2ZGFkODIyN2Q2ZGNmNmFhYjNhZDg0NWRiMzNiNDI2MmYxNjE1NDZhMjFjZjkzZjA3MzNmIiwidGFnIjoiIn0%3D</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-374360692\", {\"maxDepth\":0})</script>\n", "request_cookies": "<pre class=sf-dump id=sf-dump-949544656 data-indent-pad=\"  \"><span class=sf-dump-note>array:4</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>quiz_test_id</span>\" => \"<span class=sf-dump-str title=\"16 characters\">SIQT20250616ZNF4</span>\"\n  \"<span class=sf-dump-key>laravel_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">zOgYDYsZpjT8OLuisTWWHn0ROfzVy1aTCyMaZh59</span>\"\n  \"<span class=sf-dump-key>XSRF-TOKEN</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>si_app_session</span>\" => \"<span class=sf-dump-str title=\"40 characters\">AFMC64tXglhKprsQQSyIQPheMvMySaThtMI01Ous</span>\"\n</samp>]\n</pre><script>Sfdump(\"sf-dump-949544656\", {\"maxDepth\":0})</script>\n", "response_headers": "<pre class=sf-dump id=sf-dump-149393032 data-indent-pad=\"  \"><span class=sf-dump-note>array:3</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>cache-control</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"17 characters\">no-cache, private</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>date</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"29 characters\">Mon, 16 Jun 2025 13:49:23 GMT</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>content-type</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    <span class=sf-dump-index>0</span> => \"<span class=sf-dump-str title=\"16 characters\">application/json</span>\"\n  </samp>]\n</samp>]\n</pre><script>Sfdump(\"sf-dump-149393032\", {\"maxDepth\":0})</script>\n", "session_attributes": "<pre class=sf-dump id=sf-dump-1680283260 data-indent-pad=\"  \"><span class=sf-dump-note>array:5</span> [<samp data-depth=1 class=sf-dump-expanded>\n  \"<span class=sf-dump-key>_token</span>\" => \"<span class=sf-dump-str title=\"40 characters\">a8Vo0al9MhPtbpB5h2Ncxi4QQ3SWnfZqHOLwjwZM</span>\"\n  \"<span class=sf-dump-key>_previous</span>\" => <span class=sf-dump-note>array:1</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>url</span>\" => \"<span class=sf-dump-str title=\"62 characters\">https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA</span>\"\n  </samp>]\n  \"<span class=sf-dump-key>_flash</span>\" => <span class=sf-dump-note>array:2</span> [<samp data-depth=2 class=sf-dump-compact>\n    \"<span class=sf-dump-key>old</span>\" => []\n    \"<span class=sf-dump-key>new</span>\" => []\n  </samp>]\n  \"<span class=sf-dump-key>alert</span>\" => []\n  \"<span class=sf-dump-key>PHPDEBUGBAR_STACK_DATA</span>\" => []\n</samp>]\n</pre><script>Sfdump(\"sf-dump-1680283260\", {\"maxDepth\":0})</script>\n"}, "tooltip": {"status": "400 Bad Request", "full_url": "https://blueorange.test/application/quiz/test/J9a2D4Q0k56RodvA/submit", "action_name": "application.quiz.test.submit", "controller_action": "App\\Http\\Controllers\\PublicQuizController@submitQuiz"}, "badge": "400 Bad Request"}}