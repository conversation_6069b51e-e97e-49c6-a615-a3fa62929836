<?php $__env->startSection('page_title', __('QUIZ RESULTS')); ?>

<?php $__env->startSection('custom_css'); ?>
    <style>
        .results-header {
            background: linear-gradient(135deg, <?php echo e($passed ? '#28a745' : '#dc3545'); ?> 0%, <?php echo e($passed ? '#20c997' : '#e74c3c'); ?> 100%);
            color: white;
            padding: 40px;
            border-radius: 20px;
            margin-bottom: 30px;
            text-align: center;
            box-shadow: 0 8px 25px rgba(<?php echo e($passed ? '40, 167, 69' : '220, 53, 69'); ?>, 0.3);
        }

        .score-circle {
            width: 140px;
            height: 140px;
            border-radius: 50%;
            background: rgba(255,255,255,0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 25px;
            font-size: 2.5rem;
            font-weight: bold;
            border: 4px solid rgba(255,255,255,0.3);
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
        }

        .question-review {
            border: 1px solid #e3e6f0;
            border-radius: 15px;
            padding: 25px;
            margin-bottom: 25px;
            background: white;
            box-shadow: 0 4px 12px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }

        .question-review:hover {
            box-shadow: 0 6px 20px rgba(0,0,0,0.12);
        }

        .question-review.correct {
            border-left: 6px solid #28a745;
            background: linear-gradient(135deg, #f8fff9 0%, #ffffff 100%);
        }

        .question-review.incorrect {
            border-left: 6px solid #dc3545;
            background: linear-gradient(135deg, #fff8f8 0%, #ffffff 100%);
        }

        .question-review.unanswered {
            border-left: 6px solid #ffc107;
            background: linear-gradient(135deg, #fffdf5 0%, #ffffff 100%);
        }

        .option-review {
            padding: 15px 20px;
            margin: 10px 0;
            border-radius: 10px;
            border: 2px solid #e3e6f0;
            transition: all 0.3s ease;
        }

        .option-review.correct {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            border-color: #c3e6cb;
            color: #155724;
            font-weight: 600;
        }

        .option-review.selected-wrong {
            background: linear-gradient(135deg, #f8d7da 0%, #fce4e6 100%);
            border-color: #f5c6cb;
            color: #721c24;
            font-weight: 600;
        }

        .option-review.selected-correct {
            background: linear-gradient(135deg, #d4edda 0%, #e8f5e8 100%);
            border-color: #28a745;
            color: #155724;
            font-weight: bold;
            border-width: 3px;
        }

        .stats-card {
            background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
            border: 2px solid #696cff;
            border-radius: 15px;
            transition: all 0.3s ease;
        }

        .stats-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 8px 25px rgba(105, 108, 255, 0.15);
        }

        @media print {
            .no-print {
                display: none !important;
            }

            .results-header {
                background: <?php echo e($passed ? '#28a745' : '#dc3545'); ?> !important;
                -webkit-print-color-adjust: exact;
            }
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>
    <!-- Results Header -->
    <div class="results-header">
        <div class="score-circle">
            <?php echo e($test->total_score); ?>/<?php echo e($test->total_questions); ?>

        </div>
        <h2 class="mb-3">
            <?php if($passed): ?>
                <i class="ti ti-trophy me-2"></i>Congratulations!
            <?php else: ?>
                <i class="ti ti-x-circle me-2"></i>Better Luck Next Time
            <?php endif; ?>
        </h2>
        <h4 class="mb-3"><i class="ti ti-user me-2"></i><?php echo e($test->candidate_name); ?></h4>
        <div class="row">
            <div class="col-md-6">
                <p class="mb-2">
                    <i class="ti ti-chart-pie me-2"></i>Score: <strong><?php echo e($test->total_score); ?> out of <?php echo e($test->total_questions); ?></strong>
                </p>
                <p class="mb-2">
                    <i class="ti ti-percentage me-2"></i>Percentage: <strong><?php echo e($percentage); ?>%</strong>
                </p>
            </div>
            <div class="col-md-6">
                <p class="mb-2">
                    <i class="ti ti-target me-2"></i>Required: <strong><?php echo e($test->passing_score); ?> out of <?php echo e($test->total_questions); ?></strong>
                </p>
                <p class="mb-2">
                    <i class="ti ti-id me-2"></i>Test ID: <strong><?php echo e($test->getRouteKey()); ?></strong>
                </p>
            </div>
        </div>
        <hr style="border-color: rgba(255,255,255,0.3); margin: 20px 0;">
        <h5 class="mb-0">
            <?php if($passed): ?>
                <i class="ti ti-check-circle me-2"></i>You have successfully passed the quiz! 🎉
            <?php else: ?>
                <i class="ti ti-alert-triangle me-2"></i>You needed <?php echo e($test->passing_score); ?> to pass. Keep practicing! 💪
            <?php endif; ?>
        </h5>
    </div>

    <!-- Test Summary -->
    <div class="row mb-4">
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-list-numbers text-primary mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Total Questions</h6>
                    <h3 class="text-primary mb-0"><?php echo e($test->total_questions); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-check text-success mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Correct Answers</h6>
                    <h3 class="text-success mb-0"><?php echo e($test->total_score); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-x text-danger mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Incorrect Answers</h6>
                    <h3 class="text-danger mb-0"><?php echo e($test->attempted_questions - $test->total_score); ?></h3>
                </div>
            </div>
        </div>
        <div class="col-md-3 mb-3">
            <div class="card stats-card text-center h-100">
                <div class="card-body">
                    <i class="ti ti-clock text-warning mb-3" style="font-size: 2.5rem;"></i>
                    <h6 class="card-title text-muted">Time Taken</h6>
                    <h3 class="text-warning mb-0">
                        <?php if($test->started_at && $test->ended_at): ?>
                            <?php echo e($test->started_at->diffInMinutes($test->ended_at)); ?>m
                        <?php else: ?>
                            --
                        <?php endif; ?>
                    </h3>
                </div>
            </div>
        </div>
    </div>

    <!-- Question Review -->
    <div class="card">
        <div class="card-header header-elements">
            <h5 class="mb-0">
                <i class="ti ti-eye me-2"></i>Detailed Question Review
            </h5>
            <div class="card-header-elements ms-auto">
                <span class="badge bg-primary"><?php echo e($questions->count()); ?> Questions</span>
            </div>
        </div>
        <div class="card-body">
            <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $index => $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                <?php
                    $userAnswer = $test->answers->where('quiz_question_id', $question->id)->first();
                    $isCorrect = $userAnswer && $userAnswer->is_correct;
                    $isAnswered = $userAnswer !== null;
                ?>

                <div class="question-review <?php echo e($isAnswered ? ($isCorrect ? 'correct' : 'incorrect') : 'unanswered'); ?>">
                    <div class="d-flex justify-content-between align-items-start mb-4">
                        <div class="flex-grow-1">
                            <div class="d-flex align-items-center mb-2">
                                <span class="badge bg-primary me-3" style="font-size: 0.9rem;">Q<?php echo e($index + 1); ?></span>
                                <?php if($isAnswered): ?>
                                    <?php if($isCorrect): ?>
                                        <span class="badge bg-success">
                                            <i class="ti ti-check me-1"></i>Correct
                                        </span>
                                    <?php else: ?>
                                        <span class="badge bg-danger">
                                            <i class="ti ti-x me-1"></i>Incorrect
                                        </span>
                                    <?php endif; ?>
                                <?php else: ?>
                                    <span class="badge bg-warning">
                                        <i class="ti ti-minus me-1"></i>Not Answered
                                    </span>
                                <?php endif; ?>
                            </div>
                            <h6 class="mb-0"><?php echo e($question->question); ?></h6>
                        </div>
                    </div>

                    <div class="options">
                        <?php $__currentLoopData = ['A', 'B', 'C', 'D']; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $option): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                            <?php
                                $isUserSelected = $userAnswer && $userAnswer->selected_option === $option;
                                $isCorrectOption = $question->correct_option === $option;
                            ?>

                            <div class="option-review
                                <?php if($isCorrectOption): ?> correct <?php endif; ?>
                                <?php if($isUserSelected && $isCorrectOption): ?> selected-correct <?php endif; ?>
                                <?php if($isUserSelected && !$isCorrectOption): ?> selected-wrong <?php endif; ?>
                            ">
                                <div class="d-flex justify-content-between align-items-center">
                                    <div class="flex-grow-1">
                                        <strong><?php echo e($option); ?>.</strong> <?php echo e($question->{'option_' . strtolower($option)}); ?>

                                    </div>
                                    <div class="d-flex gap-2">
                                        <?php if($isCorrectOption): ?>
                                            <span class="badge bg-success-subtle text-success">
                                                <i class="ti ti-check"></i> Correct
                                            </span>
                                        <?php endif; ?>
                                        <?php if($isUserSelected): ?>
                                            <span class="badge bg-primary-subtle text-primary">
                                                <i class="ti ti-user"></i> Your Answer
                                            </span>
                                        <?php endif; ?>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                    </div>
                </div>
            <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
        </div>
    </div>

    <!-- Test Details -->
    <div class="card mt-4">
        <div class="card-header">
            <h5 class="mb-0">
                <i class="ti ti-info-circle me-2"></i>Test Information
            </h5>
        </div>
        <div class="card-body">
            <div class="row">
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label text-muted">Test ID</label>
                        <p class="fw-semibold"><?php echo e($test->getRouteKey()); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Candidate Name</label>
                        <p class="fw-semibold"><?php echo e($test->candidate_name); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Email Address</label>
                        <p class="fw-semibold"><?php echo e($test->candidate_email); ?></p>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="mb-3">
                        <label class="form-label text-muted">Started At</label>
                        <p class="fw-semibold"><?php echo e($test->started_at?->format('M d, Y h:i A') ?? 'N/A'); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Completed At</label>
                        <p class="fw-semibold"><?php echo e($test->ended_at?->format('M d, Y h:i A') ?? 'N/A'); ?></p>
                    </div>
                    <div class="mb-3">
                        <label class="form-label text-muted">Submission Type</label>
                        <p class="fw-semibold">
                            <?php if($test->auto_submitted): ?>
                                <span class="badge bg-warning">
                                    <i class="ti ti-clock me-1"></i>Auto Submitted (Time Expired)
                                </span>
                            <?php else: ?>
                                <span class="badge bg-success">
                                    <i class="ti ti-check me-1"></i>Manual Submission
                                </span>
                            <?php endif; ?>
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Actions -->
    <div class="text-center mt-4 no-print">
        <div class="d-flex justify-content-center gap-3 flex-wrap">
            <a href="<?php echo e(url('/')); ?>" class="btn btn-outline-primary">
                <i class="ti ti-home me-2"></i>Go to Homepage
            </a>
            <button class="btn btn-outline-secondary" onclick="window.print()">
                <i class="ti ti-printer me-2"></i>Print Results
            </button>
            <button class="btn btn-outline-info" onclick="shareResults()">
                <i class="ti ti-share me-2"></i>Share Results
            </button>
        </div>
    </div>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    <script>
        function shareResults() {
            if (navigator.share) {
                navigator.share({
                    title: 'Quiz Results - <?php echo e($test->candidate_name); ?>',
                    text: 'I scored <?php echo e($test->total_score); ?>/<?php echo e($test->total_questions); ?> (<?php echo e($percentage); ?>%) on the quiz!',
                    url: window.location.href
                });
            } else {
                // Fallback for browsers that don't support Web Share API
                const url = window.location.href;
                navigator.clipboard.writeText(url).then(() => {
                    alert('Results URL copied to clipboard!');
                });
            }
        }

        // Print optimization
        window.addEventListener('beforeprint', function() {
            document.title = 'Quiz Results - <?php echo e($test->candidate_name); ?> - <?php echo e($test->getRouteKey()); ?>';
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.public.quiz', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/public/quiz/results.blade.php ENDPATH**/ ?>