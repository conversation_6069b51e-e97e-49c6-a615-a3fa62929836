<?php $__env->startSection('meta_tags'); ?>
    
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_title', __('Create Quiz Test')); ?>

<?php $__env->startSection('css_links'); ?>
    
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/select2/select2.css')); ?>" />
    <link rel="stylesheet" href="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.css')); ?>" />
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_css'); ?>
    
    <style>
        input[type="number"]::-webkit-outer-spin-button,
        input[type="number"]::-webkit-inner-spin-button {
            -webkit-appearance: none;
            margin: 0;
        }

        input[type="number"] {
            -moz-appearance: textfield;
        }

        .quiz-form .form-label {
            font-weight: 600;
        }

        .quiz-form .required::after {
            content: " *";
            color: red;
        }

        .select2-questions {
            width: 100% !important;
        }

        .select2-container--default .select2-selection--multiple {
            min-height: 120px;
            border: 1px solid #d9dee3;
            border-radius: 0.375rem;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice {
            background-color: #696cff;
            border: 1px solid #696cff;
            color: white;
            border-radius: 0.25rem;
            padding: 2px 8px;
            margin: 2px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--default .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #ffcccc;
        }

        .question-counter {
            font-size: 0.875rem;
            color: #6c757d;
        }

        .auto-select-info {
            background-color: #e7f3ff;
            border: 1px solid #b8daff;
            border-radius: 0.375rem;
            padding: 1rem;
            margin-bottom: 1rem;
        }
    </style>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('page_name'); ?>
    <b class="text-uppercase"><?php echo e(__('Create Quiz Test')); ?></b>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('breadcrumb'); ?>
    <li class="breadcrumb-item"><?php echo e(__('Quiz')); ?></li>
    <li class="breadcrumb-item">
        <a href="<?php echo e(route('administration.quiz.test.index')); ?>"><?php echo e(__('All Tests')); ?></a>
    </li>
    <li class="breadcrumb-item active"><?php echo e(__('Create Test')); ?></li>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('content'); ?>

<!-- Start row -->
<div class="row justify-content-center">
    <div class="col-md-12">
        <div class="card mb-4">
            <div class="card-header header-elements">
                <h5 class="mb-0"><?php echo e(__('Create New Quiz Test')); ?></h5>
                <div class="card-header-elements ms-auto">
                    <a href="<?php echo e(route('administration.quiz.test.index')); ?>" class="btn btn-sm btn-outline-secondary">
                        <span class="tf-icon ti ti-arrow-left ti-xs me-1"></span>
                        <?php echo e(__('Back to Tests')); ?>

                    </a>
                </div>
            </div>

            <div class="card-body quiz-form">
                <form action="<?php echo e(route('administration.quiz.test.store')); ?>" method="POST" id="quizTestForm" autocomplete="off">
                    <?php echo csrf_field(); ?>

                    <!-- Candidate Information Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold"><?php echo e(__('Candidate Information')); ?></h6>
                            <hr class="mt-0">
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="candidate_name" class="form-label required"><?php echo e(__('Candidate Name')); ?></label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text"><i class="ti ti-user"></i></span>
                                <input type="text" id="candidate_name" name="candidate_name"
                                       value="<?php echo e(old('candidate_name')); ?>"
                                       placeholder="<?php echo e(__('Enter candidate full name')); ?>"
                                       class="form-control <?php $__errorArgs = ['candidate_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       required autofocus />
                            </div>
                            <?php $__errorArgs = ['candidate_name'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-6">
                            <label for="candidate_email" class="form-label required"><?php echo e(__('Candidate Email')); ?></label>
                            <div class="input-group input-group-merge">
                                <span class="input-group-text"><i class="ti ti-mail"></i></span>
                                <input type="email" id="candidate_email" name="candidate_email"
                                       value="<?php echo e(old('candidate_email')); ?>"
                                       placeholder="<?php echo e(__('Enter candidate email address')); ?>"
                                       class="form-control <?php $__errorArgs = ['candidate_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       required />
                            </div>
                            <?php $__errorArgs = ['candidate_email'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Test Configuration Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold"><?php echo e(__('Test Configuration')); ?></h6>
                            <hr class="mt-0">
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="total_questions" class="form-label required"><?php echo e(__('Total Questions')); ?></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-list-numbers"></i></span>
                                <input type="number" id="total_questions" name="total_questions"
                                       value="<?php echo e(old('total_questions', 10)); ?>"
                                       min="1" max="100" step="1"
                                       class="form-control <?php $__errorArgs = ['total_questions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       required />
                            </div>
                            <div class="form-text"><?php echo e(__('Number of questions for this test (1-100)')); ?></div>
                            <?php $__errorArgs = ['total_questions'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="total_time" class="form-label required"><?php echo e(__('Time Limit (Minutes)')); ?></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-clock"></i></span>
                                <input type="number" id="total_time" name="total_time"
                                       value="<?php echo e(old('total_time', 10)); ?>"
                                       min="1" max="300" step="1"
                                       class="form-control <?php $__errorArgs = ['total_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       required />
                            </div>
                            <div class="form-text"><?php echo e(__('Time limit in minutes (1-300)')); ?></div>
                            <?php $__errorArgs = ['total_time'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>

                        <div class="mb-3 col-md-4">
                            <label for="passing_score" class="form-label required"><?php echo e(__('Passing Score')); ?></label>
                            <div class="input-group">
                                <span class="input-group-text"><i class="ti ti-target"></i></span>
                                <input type="number" id="passing_score" name="passing_score"
                                       value="<?php echo e(old('passing_score', 6)); ?>"
                                       min="1" step="1"
                                       class="form-control <?php $__errorArgs = ['passing_score'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>"
                                       required />
                            </div>
                            <div class="form-text"><?php echo e(__('Minimum score required to pass')); ?></div>
                            <?php $__errorArgs = ['passing_score'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                            <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                        </div>
                    </div>

                    <!-- Question Selection Section -->
                    <div class="row">
                        <div class="col-12">
                            <h6 class="text-muted fw-semibold"><?php echo e(__('Question Selection')); ?></h6>
                            <hr class="mt-0">
                        </div>

                        <div class="col-12">
                            <div class="auto-select-info">
                                <div class="d-flex align-items-center">
                                    <i class="ti ti-info-circle text-info me-2"></i>
                                    <div>
                                        <strong><?php echo e(__('Auto Selection Mode')); ?></strong>
                                        <p class="mb-0 text-muted"><?php echo e(__('Leave questions unselected to automatically choose random questions from the active question bank. Or manually select specific questions below.')); ?></p>
                                    </div>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label for="question_ids" class="form-label"><?php echo e(__('Select Questions (Optional)')); ?></label>
                                <div class="question-counter mb-2">
                                    <span id="selected-count">0</span> <?php echo e(__('of')); ?> <span id="max-questions"><?php echo e(old('total_questions', 10)); ?></span> <?php echo e(__('questions can be selected')); ?>

                                    <span class="text-muted ms-2">(<?php echo e($questions->count()); ?> <?php echo e(__('total available')); ?>)</span>
                                </div>

                                <?php if($questions->count() > 0): ?>
                                    <select name="question_ids[]" id="question_ids" class="form-select select2-questions <?php $__errorArgs = ['question_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?> is-invalid <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>" multiple data-placeholder="<?php echo e(__('Search and select questions...')); ?>">
                                        <?php $__currentLoopData = $questions; $__env->addLoop($__currentLoopData); foreach($__currentLoopData as $question): $__env->incrementLoopIndices(); $loop = $__env->getLastLoop(); ?>
                                            <option value="<?php echo e($question->id); ?>"
                                                    <?php echo e(in_array($question->id, old('question_ids', [])) ? 'selected' : ''); ?>

                                                    data-question="<?php echo e($question->question); ?>"
                                                    data-correct="<?php echo e($question->correct_option); ?>">
                                                Q<?php echo e($loop->iteration); ?>: <?php echo e(Str::limit($question->question, 100)); ?>

                                            </option>
                                        <?php endforeach; $__env->popLoop(); $loop = $__env->getLastLoop(); ?>
                                    </select>
                                    <div class="form-text">
                                        <i class="ti ti-info-circle me-1"></i>
                                        <?php echo e(__('You can search questions by typing. Maximum selections will be limited based on "Total Questions" field.')); ?>

                                    </div>
                                <?php else: ?>
                                    <div class="alert alert-warning">
                                        <i class="ti ti-alert-triangle me-2"></i>
                                        <?php echo e(__('No active questions available. Please add questions first.')); ?>

                                        <a href="<?php echo e(route('administration.quiz.question.create')); ?>" class="btn btn-sm btn-warning ms-2">
                                            <i class="ti ti-plus me-1"></i><?php echo e(__('Add Questions')); ?>

                                        </a>
                                    </div>
                                <?php endif; ?>

                                <?php $__errorArgs = ['question_ids'];
$__bag = $errors->getBag($__errorArgs[1] ?? 'default');
if ($__bag->has($__errorArgs[0])) :
if (isset($message)) { $__messageOriginal = $message; }
$message = $__bag->first($__errorArgs[0]); ?>
                                    <b class="text-danger"><i class="ti ti-info-circle me-1"></i><?php echo e($message); ?></b>
                                <?php unset($message);
if (isset($__messageOriginal)) { $message = $__messageOriginal; }
endif;
unset($__errorArgs, $__bag); ?>
                            </div>
                        </div>
                    </div>

                    <!-- Submit Buttons -->
                    <div class="row">
                        <div class="col-12">
                            <hr class="my-4">
                            <div class="d-flex justify-content-end gap-2">
                                <button type="reset" class="btn btn-outline-secondary" onclick="return confirm('<?php echo e(__('Are you sure you want to reset the form?')); ?>')">
                                    <i class="ti ti-refresh me-1"></i><?php echo e(__('Reset Form')); ?>

                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <i class="ti ti-device-floppy me-1"></i><?php echo e(__('Create Test')); ?>

                                </button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>
<!-- End row -->

<?php $__env->stopSection(); ?>

<?php $__env->startSection('script_links'); ?>
    
    <script src="<?php echo e(asset('assets/js/form-layouts.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/select2/select2.js')); ?>"></script>
    <script src="<?php echo e(asset('assets/vendor/libs/bootstrap-select/bootstrap-select.js')); ?>"></script>
<?php $__env->stopSection(); ?>

<?php $__env->startSection('custom_script'); ?>
    
    <script>
        $(document).ready(function() {
            let maxQuestions = parseInt($('#total_questions').val()) || 10;

            // Initialize Select2 for questions with maximum selection limit
            function initializeQuestionSelect2() {
                $('#question_ids').select2({
                    placeholder: '<?php echo e(__('Search and select questions...')); ?>',
                    allowClear: true,
                    closeOnSelect: false,
                    maximumSelectionLength: maxQuestions,
                    language: {
                        maximumSelected: function (e) {
                            return '<?php echo e(__('You can only select')); ?> ' + e.maximum + ' <?php echo e(__('questions based on the Total Questions field')); ?>';
                        }
                    },
                    templateResult: function(data) {
                        if (!data.id) {
                            return data.text;
                        }

                        // Create custom template for dropdown options
                        var $result = $('<div class="select2-question-option"></div>');
                        $result.html('<strong>' + data.text + '</strong>');

                        return $result;
                    },
                    templateSelection: function(data) {
                        if (!data.id) {
                            return data.text;
                        }

                        // Show just the question number for selected items
                        var questionText = data.text;
                        var questionNumber = questionText.match(/^Q\d+/);
                        return questionNumber ? questionNumber[0] : data.text;
                    }
                }).on('change', function() {
                    updateSelectedCount();
                });
            }

            // Initialize other Select2 elements
            $('.select2:not(#question_ids)').select2({
                placeholder: 'Select value',
                allowClear: true
            });

            // Initialize question Select2
            initializeQuestionSelect2();

            // Update selected count
            function updateSelectedCount() {
                const selectedCount = $('#question_ids').val() ? $('#question_ids').val().length : 0;
                $('#selected-count').text(selectedCount);
            }

            // Update total questions and max selection limit
            $('#total_questions').on('input', function() {
                const totalQuestions = parseInt($(this).val()) || 1;
                maxQuestions = totalQuestions;

                // Update max questions display
                $('#max-questions').text(maxQuestions);

                // Update passing score max value
                $('#passing_score').attr('max', totalQuestions);

                // Adjust passing score if it exceeds total questions
                const currentPassingScore = parseInt($('#passing_score').val()) || 0;
                if (currentPassingScore > totalQuestions) {
                    $('#passing_score').val(totalQuestions);
                }

                // Reinitialize Select2 with new maximum selection limit
                $('#question_ids').select2('destroy');
                initializeQuestionSelect2();
                updateSelectedCount();
            });

            // Validate passing score doesn't exceed total questions
            $('#passing_score').on('input', function() {
                const totalQuestions = parseInt($('#total_questions').val()) || 1;
                const passingScore = parseInt($(this).val()) || 0;

                if (passingScore > totalQuestions) {
                    $(this).val(totalQuestions);
                    alert('<?php echo e(__('Passing score cannot be greater than total questions.')); ?>');
                }
            });

            // Form validation before submit
            $('#quizTestForm').on('submit', function(e) {
                const totalQuestions = parseInt($('#total_questions').val()) || 0;
                const passingScore = parseInt($('#passing_score').val()) || 0;
                const selectedQuestions = $('#question_ids').val() ? $('#question_ids').val().length : 0;

                // Check if we have enough questions
                if (selectedQuestions > 0 && selectedQuestions < totalQuestions) {
                    if (!confirm('<?php echo e(__('You have selected fewer questions than the total questions count. The remaining questions will be randomly selected. Continue?')); ?>')) {
                        e.preventDefault();
                        return false;
                    }
                }

                // Check if no questions are available for auto-selection
                if (selectedQuestions === 0 && totalQuestions > <?php echo e($questions->count()); ?>) {
                    alert('<?php echo e(__('Not enough questions available. Please reduce the total questions count or add more questions.')); ?>');
                    e.preventDefault();
                    return false;
                }

                // Validate passing score
                if (passingScore > totalQuestions) {
                    alert('<?php echo e(__('Passing score cannot be greater than total questions.')); ?>');
                    e.preventDefault();
                    return false;
                }
            });

            // Set initial values
            $('#total_questions').trigger('input');
            updateSelectedCount();
        });
    </script>
<?php $__env->stopSection(); ?>

<?php echo $__env->make('layouts.administration.app', \Illuminate\Support\Arr::except(get_defined_vars(), ['__data', '__path']))->render(); ?><?php /**PATH E:\NIGEL\LaravelProjects\laragon\www\BlueOrange\resources\views/administration/quiz/test/create.blade.php ENDPATH**/ ?>